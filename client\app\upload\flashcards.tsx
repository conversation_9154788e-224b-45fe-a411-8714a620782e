"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Upload,
  Brain,
  Trash2,
  Plus,
  X,
  Eye,
  Edit,
  Save
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

// Simplified Flashcard interface
interface Flashcard {
  id: string;
  pregunta: string;
  respuesta: string;
  etiquetas?: string[];
  created_at: string;
}

interface FlashcardForm {
  id: string;
  pregunta: string;
  respuesta: string;
  etiquetas: string[];
}

interface FlashcardsComponentProps {}

// Helper function to get API base URL
function getApiBaseUrl(): string {
  const base = process.env.NEXT_PUBLIC_API_BASE_URL?.replace(/\/$/, '');
  return base || 'http://localhost:8000';
}

export default function FlashcardsComponent({}: FlashcardsComponentProps) {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [jsonContent, setJsonContent] = useState('');
  const [formData, setFormData] = useState<FlashcardForm>({
    id: '',
    pregunta: '',
    respuesta: '',
    etiquetas: []
  });
  const [tagInput, setTagInput] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);


  // Fetch flashcards
  const fetchFlashcards = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards`);
      if (!response.ok) throw new Error('Error al cargar las flashcards');
      const data = await response.json();
      setFlashcards(data);
    } catch (err) {
      console.error('Error fetching flashcards:', err);
      setError('Error al cargar las flashcards');
    }
  };

  // Load flashcards on mount
  useEffect(() => {
    fetchFlashcards();
  }, []);

  // Handle JSON content submission
  const handleJsonSubmit = async () => {
    if (!jsonContent.trim()) {
      setError('Por favor ingrese el contenido JSON');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const formData = new FormData();
      formData.append('json_content', jsonContent);

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards/json`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar el contenido JSON');
      }

      const result = await response.json();

      if (result.success) {
        setSuccess(true);
        setError(null);
        setJsonContent('');
        fetchFlashcards();
      } else {
        throw new Error(result.message || 'Error al procesar el contenido JSON');
      }
    } catch (err) {
      console.error('Error processing JSON:', err);
      setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle manual form submission
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.id.trim() || !formData.pregunta.trim() || !formData.respuesta.trim()) {
      setError('Por favor complete el ID, pregunta y respuesta');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const submitData = new FormData();
      submitData.append('id', formData.id);
      submitData.append('pregunta', formData.pregunta);
      submitData.append('respuesta', formData.respuesta);
      if (formData.etiquetas.length > 0) submitData.append('etiquetas', JSON.stringify(formData.etiquetas));

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards`, {
        method: 'POST',
        body: submitData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al crear la flashcard');
      }

      const result = await response.json();

      if (result.success) {
        setSuccess(true);
        resetForm();
        fetchFlashcards();
      } else {
        throw new Error(result.message || 'Error al crear la flashcard');
      }
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Error al crear la flashcard');
    } finally {
      setIsUploading(false);
    }
  };

  // Add tag
  const addTag = () => {
    if (tagInput.trim() && !formData.etiquetas.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        etiquetas: [...prev.etiquetas, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      etiquetas: prev.etiquetas.filter(tag => tag !== tagToRemove)
    }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      id: '',
      pregunta: '',
      respuesta: '',
      etiquetas: []
    });
    setTagInput('');
    setJsonContent('');
    setError(null);
    setSuccess(false);
    setEditingId(null);
  };

  // Handle delete
  const handleDelete = async (flashcardId: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard?')) return;

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards/${flashcardId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar la flashcard');

      setFlashcards(flashcards.filter(f => f.id !== flashcardId));
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error deleting flashcard:', err);
      setError('Error al eliminar la flashcard');
    }
  };

  // Handle edit
  const handleEdit = (flashcard: Flashcard) => {
    setFormData({
      id: flashcard.id,
      pregunta: flashcard.pregunta,
      respuesta: flashcard.respuesta,
      etiquetas: flashcard.etiquetas || []
    });
    setEditingId(flashcard.id);
  };

  // Handle update
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingId) return;

    setIsUploading(true);
    setError(null);

    try {
      const submitData = new FormData();
      if (formData.id !== editingId) submitData.append('id', formData.id);
      submitData.append('pregunta', formData.pregunta);
      submitData.append('respuesta', formData.respuesta);
      if (formData.etiquetas.length > 0) submitData.append('etiquetas', JSON.stringify(formData.etiquetas));

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards/${editingId}`, {
        method: 'PUT',
        body: submitData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al actualizar la flashcard');
      }

      const result = await response.json();

      if (result.success) {
        setSuccess(true);
        resetForm();
        fetchFlashcards();
      } else {
        throw new Error(result.message || 'Error al actualizar la flashcard');
      }
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Error al actualizar la flashcard');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Brain className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Flashcards</h1>
          <Badge variant="outline" className="ml-2">{flashcards.length} tarjetas</Badge>
        </div>
      </div>

      {/* Create/Edit Form */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Plus className="h-5 w-5" />
            {editingId ? 'Editar Flashcard' : 'Nueva Flashcard'}
          </CardTitle>
        </CardHeader>
        <CardContent>
            {/* JSON Upload Section */}
            <div className="mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Upload className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium text-blue-900">Crear desde JSON</h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                Pega el contenido JSON para crear múltiples flashcards de una vez.
              </p>
              <div className="space-y-3">
                <Textarea
                  id="json-content"
                  placeholder="Pega aquí el contenido JSON..."
                  value={jsonContent}
                  onChange={(e) => setJsonContent(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <Button
                  type="button"
                  onClick={handleJsonSubmit}
                  disabled={!jsonContent.trim() || isUploading}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="h-4 w-4" />
                  {isUploading ? 'Procesando...' : 'Crear desde JSON'}
                </Button>
              </div>

              {/* JSON Format Example */}
              <details className="mt-3">
                <summary className="text-sm text-blue-700 cursor-pointer hover:text-blue-800">
                  Ver formato JSON requerido
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto">
                  <pre>{`[
  {
    "id": "hta_fc_001",
    "pregunta": "¿Cuál es la definición de hipertensión arterial según la OMS?",
    "respuesta": "Presión arterial sistólica ≥ 140 mmHg o diastólica ≥ 90 mmHg en al menos dos mediciones en días diferentes.",
    "etiquetas": ["definición", "diagnóstico", "OMS"]
  }
]`}</pre>
                </div>
              </details>
            </div>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">O crear manualmente</span>
              </div>
            </div>

            <form onSubmit={editingId ? handleUpdate : handleFormSubmit} className="space-y-6">
              {/* ID */}
              <div className="space-y-2">
                <Label htmlFor="id" className="text-sm font-medium">ID</Label>
                <Input
                  id="id"
                  placeholder="ej: hta_fc_001"
                  value={formData.id}
                  onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                  required
                />
              </div>

              {/* Question */}
              <div className="space-y-2">
                <Label htmlFor="pregunta" className="text-sm font-medium">Pregunta</Label>
                <Textarea
                  id="pregunta"
                  placeholder="¿Cuál es la definición de hipertensión arterial según la OMS?"
                  value={formData.pregunta}
                  onChange={(e) => setFormData(prev => ({ ...prev, pregunta: e.target.value }))}
                  rows={3}
                  className="resize-none"
                  required
                />
              </div>

              {/* Answer */}
              <div className="space-y-2">
                <Label htmlFor="respuesta" className="text-sm font-medium">Respuesta</Label>
                <Textarea
                  id="respuesta"
                  placeholder="Presión arterial sistólica ≥ 140 mmHg o diastólica ≥ 90 mmHg en al menos dos mediciones en días diferentes."
                  value={formData.respuesta}
                  onChange={(e) => setFormData(prev => ({ ...prev, respuesta: e.target.value }))}
                  rows={4}
                  className="resize-none"
                  required
                />
                <p className="text-xs text-gray-500">
                  Puedes incluir imágenes usando markdown: ![descripción](url)
                </p>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Etiquetas</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Agregar etiqueta..."
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {formData.etiquetas.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.etiquetas.map((tag, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Alerts */}
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50">
                  <AlertDescription className="text-green-800">
                    ¡Flashcard {editingId ? 'actualizada' : 'creada'} exitosamente!
                  </AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <div className="flex justify-end gap-2 pt-4">
                {editingId && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={resetForm}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancelar
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isUploading}
                  className="min-w-[120px]"
                >
                  {isUploading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      {editingId ? 'Actualizando...' : 'Creando...'}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      {editingId ? <Save className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      {editingId ? 'Actualizar' : 'Crear'} Flashcard
                    </div>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

      {/* Flashcards List */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg">Lista de Flashcards</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {flashcards.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p className="text-sm">No hay flashcards aún</p>
              </div>
            ) : (
              flashcards.map((flashcard) => (
                <div key={flashcard.id} className="group border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="text-xs font-mono">
                          {flashcard.id}
                        </Badge>
                        {flashcard.etiquetas && flashcard.etiquetas.length > 0 && (
                          <div className="flex gap-1">
                            {flashcard.etiquetas.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {flashcard.etiquetas.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{flashcard.etiquetas.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      <p className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                        {flashcard.pregunta}
                      </p>
                      <p className="text-xs text-gray-600 line-clamp-2">
                        {flashcard.respuesta}
                      </p>
                    </div>
                    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                              <Badge variant="outline" className="font-mono">
                                {flashcard.id}
                              </Badge>
                              Flashcard
                            </DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <h3 className="font-medium text-sm text-gray-700 mb-2">Pregunta:</h3>
                              <p className="text-gray-900">{flashcard.pregunta}</p>
                            </div>
                            <div>
                              <h3 className="font-medium text-sm text-gray-700 mb-2">Respuesta:</h3>
                              <div className="prose prose-sm max-w-none">
                                {flashcard.respuesta.split('\n').map((line, index) => (
                                  <p key={index}>{line}</p>
                                ))}
                              </div>
                            </div>
                            {flashcard.etiquetas && flashcard.etiquetas.length > 0 && (
                              <div>
                                <h3 className="font-medium text-sm text-gray-700 mb-2">Etiquetas:</h3>
                                <div className="flex flex-wrap gap-1">
                                  {flashcard.etiquetas.map((tag, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(flashcard)}
                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(flashcard.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
    </div>
  );
}