"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  Upload, 
  Brain,
  Trash2,
  Plus,
  X,
  Eye
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface EspecialidadTemaData {
  especialidad: string
  tema: string
  titulo: string
}

interface FlashcardsComponentProps {
  sharedData: EspecialidadTemaData
}

interface Flashcard {
  id: number;
  title: string;
  especialidad: string;
  tema: string;
  question: string;
  answer: string;
  explanation?: string;
  difficulty?: string;
  category?: string;
  tags?: string[];
  created_at: string;
}

interface FlashcardForm {
  question: string;
  answer: string;
  explanation: string;
  difficulty: string;
  category: string;
  tags: string[];
}

// Helper function to get API base URL
function getApiBaseUrl(): string {
  const base = process.env.NEXT_PUBLIC_API_BASE_URL?.replace(/\/$/, '');
  return base || 'http://localhost:8000';
}

export default function FlashcardsComponent({ sharedData }: FlashcardsComponentProps) {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [jsonContent, setJsonContent] = useState('');
  const [formData, setFormData] = useState<FlashcardForm>({
    question: '',
    answer: '',
    explanation: '',
    difficulty: '',
    category: '',
    tags: []
  });
  const [tagInput, setTagInput] = useState('');

  // Fetch flashcards
  const fetchFlashcards = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards`);
      if (!response.ok) throw new Error('Error al cargar las flashcards');
      const data = await response.json();
      setFlashcards(data);
    } catch (err) {
      console.error('Error fetching flashcards:', err);
      setError('Error al cargar las flashcards');
    }
  };

  // Load flashcards on mount
  useEffect(() => {
    fetchFlashcards();
  }, []);

  // Handle JSON content submission
  const handleJsonSubmit = async () => {
    if (!jsonContent.trim()) {
      setError('Por favor ingrese el contenido JSON');
      return;
    }

    if (!sharedData.especialidad || !sharedData.tema) {
      setError('Por favor complete la información general (especialidad y tema) antes de procesar el JSON');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const formData = new FormData();
      formData.append('json_content', jsonContent);
      formData.append('especialidad', sharedData.especialidad);
      formData.append('tema', sharedData.tema);
      if (sharedData.titulo) {
        formData.append('titulo', sharedData.titulo);
      }

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards/json`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar el contenido JSON');
      }

      const result = await response.json();
      
      if (result.success) {
        setSuccess(true);
        setError(null);
        setJsonContent('');
        fetchFlashcards();
      } else {
        throw new Error(result.message || 'Error al procesar el contenido JSON');
      }
    } catch (err) {
      console.error('Error processing JSON:', err);
      setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle manual form submission
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!sharedData.especialidad || !sharedData.tema || !sharedData.titulo) {
      setError('Por favor complete la información general (especialidad, tema y título)');
      return;
    }

    if (!formData.question.trim() || !formData.answer.trim()) {
      setError('Por favor complete la pregunta y respuesta');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const submitData = new FormData();
      submitData.append('especialidad', sharedData.especialidad);
      submitData.append('tema', sharedData.tema);
      submitData.append('titulo', sharedData.titulo);
      submitData.append('question', formData.question);
      submitData.append('answer', formData.answer);
      if (formData.explanation) submitData.append('explanation', formData.explanation);
      if (formData.difficulty) submitData.append('difficulty', formData.difficulty);
      if (formData.category) submitData.append('category', formData.category);
      if (formData.tags.length > 0) submitData.append('tags', JSON.stringify(formData.tags));

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards`, {
        method: 'POST',
        body: submitData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al crear la flashcard');
      }

      const result = await response.json();
      
      if (result.success) {
        setSuccess(true);
        resetForm();
        fetchFlashcards();
      } else {
        throw new Error(result.message || 'Error al crear la flashcard');
      }
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Error al crear la flashcard');
    } finally {
      setIsUploading(false);
    }
  };

  // Add tag
  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  // Remove tag
  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      question: '',
      answer: '',
      explanation: '',
      difficulty: '',
      category: '',
      tags: []
    });
    setTagInput('');
    setJsonContent('');
    setError(null);
    setSuccess(false);
  };

  // Handle delete
  const handleDelete = async (flashcardId: number) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard?')) return;

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/flashcards/${flashcardId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar la flashcard');

      setFlashcards(flashcards.filter(f => f.id !== flashcardId));
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error deleting flashcard:', err);
      setError('Error al eliminar la flashcard');
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Form */}
      <div className="lg:col-span-2">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Brain className="h-5 w-5" />
              Nueva Flashcard
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* JSON Upload Section */}
            <div className="mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Upload className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium text-blue-900">Crear desde JSON</h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                Pega el contenido JSON para crear múltiples flashcards de una vez.
              </p>
              <div className="space-y-3">
                <Textarea
                  id="json-content"
                  placeholder="Pega aquí el contenido JSON..."
                  value={jsonContent}
                  onChange={(e) => setJsonContent(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <Button
                  type="button"
                  onClick={handleJsonSubmit}
                  disabled={!jsonContent.trim() || isUploading}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="h-4 w-4" />
                  {isUploading ? 'Procesando...' : 'Crear desde JSON'}
                </Button>
              </div>
              
              {/* JSON Format Example */}
              <details className="mt-3">
                <summary className="text-sm text-blue-700 cursor-pointer hover:text-blue-800">
                  Ver formato JSON requerido
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto">
                  <pre>{`[
  {
    "id": "1",
    "question": "¿Cuál es la función principal del corazón?",
    "answer": "Bombear sangre a través del sistema circulatorio",
    "explanation": "El corazón actúa como una bomba muscular...",
    "difficulty": "easy",
    "category": "Anatomía",
    "tags": ["cardiología", "anatomía", "fisiología"]
  }
]`}</pre>
                </div>
              </details>
            </div>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">O crear manualmente</span>
              </div>
            </div>

            <form onSubmit={handleFormSubmit} className="space-y-6">
              {/* Question */}
              <div className="space-y-2">
                <Label htmlFor="question" className="text-sm font-medium">Pregunta</Label>
                <Textarea
                  id="question"
                  placeholder="¿Cuál es la función principal del corazón?"
                  value={formData.question}
                  onChange={(e) => setFormData(prev => ({ ...prev, question: e.target.value }))}
                  rows={3}
                  className="resize-none"
                  required
                />
              </div>

              {/* Answer */}
              <div className="space-y-2">
                <Label htmlFor="answer" className="text-sm font-medium">Respuesta</Label>
                <Textarea
                  id="answer"
                  placeholder="Bombear sangre a través del sistema circulatorio..."
                  value={formData.answer}
                  onChange={(e) => setFormData(prev => ({ ...prev, answer: e.target.value }))}
                  rows={3}
                  className="resize-none"
                  required
                />
              </div>

              {/* Explanation */}
              <div className="space-y-2">
                <Label htmlFor="explanation" className="text-sm font-medium">Explicación (opcional)</Label>
                <Textarea
                  id="explanation"
                  placeholder="Explicación detallada..."
                  value={formData.explanation}
                  onChange={(e) => setFormData(prev => ({ ...prev, explanation: e.target.value }))}
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Difficulty and Category */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Dificultad</Label>
                  <Select value={formData.difficulty} onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar dificultad" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Fácil</SelectItem>
                      <SelectItem value="medium">Medio</SelectItem>
                      <SelectItem value="hard">Difícil</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category" className="text-sm font-medium">Categoría</Label>
                  <Input
                    id="category"
                    placeholder="Ej: Anatomía, Fisiología..."
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Tags</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Agregar tag..."
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {tag}
                        <X 
                          className="h-3 w-3 cursor-pointer" 
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Alerts */}
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50">
                  <AlertDescription className="text-green-800">
                    ¡Flashcard creada exitosamente!
                  </AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <div className="flex justify-end pt-4">
                <Button 
                  type="submit" 
                  disabled={isUploading || !sharedData.especialidad || !sharedData.tema || !sharedData.titulo}
                  className="min-w-[120px]"
                >
                  {isUploading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Creando...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Crear Flashcard
                    </div>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Flashcards List */}
      <div className="lg:col-span-1">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg">Flashcards ({flashcards.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {flashcards.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">No hay flashcards aún</p>
                </div>
              ) : (
                flashcards.map((flashcard) => (
                  <div key={flashcard.id} className="group border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{flashcard.title}</h3>
                        <div className="flex items-center gap-1 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {flashcard.especialidad}
                          </Badge>
                          {flashcard.difficulty && (
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                flashcard.difficulty === 'easy' ? 'border-green-300 text-green-700' :
                                flashcard.difficulty === 'medium' ? 'border-yellow-300 text-yellow-700' :
                                'border-red-300 text-red-700'
                              }`}
                            >
                              {flashcard.difficulty === 'easy' ? 'Fácil' : 
                               flashcard.difficulty === 'medium' ? 'Medio' : 'Difícil'}
                            </Badge>
                          )}
                          {flashcard.category && (
                            <Badge variant="outline" className="text-xs">
                              {flashcard.category}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1 truncate">{flashcard.tema}</p>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{flashcard.question}</p>
                      </div>
                      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(flashcard.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}