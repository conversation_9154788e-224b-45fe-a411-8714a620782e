from __future__ import annotations

from datetime import datetime
import json

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import Optional, List

from ....core.config import get_settings
from ....db.session import get_db_session
from ....upload_content import crud, schemas
from ....services.gcs_storage import upload_file_to_gcs, delete_file_from_gcs

router = APIRouter()
settings = get_settings()

@router.post("/upload-video")
async def upload_video_clase(
    file: Optional[UploadFile] = File(None),
    thumbnail: Optional[UploadFile] = File(None),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    quiz: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    try:
        # Handle video upload
        video_url = None
        video_file_path = None
        if file:
            # Validate video file type
            allowed_video_extensions = {"mp4", "webm", "mov", "avi", "mkv"}
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in allowed_video_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de video no permitido")

            # Upload video to Google Cloud Storage
            video_file_path = f"videoclases/{especialidad}/{tema}/{datetime.utcnow().timestamp()}.{file_extension}"
            video_url = await upload_file_to_gcs(
                file=file,
                file_path=video_file_path,
                content_type=f"video/{file_extension}"
            )

        # Handle thumbnail upload
        thumbnail_url = None
        if thumbnail:
            # Validate thumbnail file type
            allowed_image_extensions = {"jpg", "jpeg", "png", "webp"}
            thumbnail_extension = thumbnail.filename.split(".")[-1].lower()
            if thumbnail_extension not in allowed_image_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de thumbnail no permitido")

            # Upload thumbnail to Google Cloud Storage
            thumbnail_file_path = f"thumbnails/{especialidad}/{tema}/{datetime.utcnow().timestamp()}.{thumbnail_extension}"
            thumbnail_url = await upload_file_to_gcs(
                file=thumbnail,
                file_path=thumbnail_file_path,
                content_type=f"image/{thumbnail_extension}"
            )

        # Create videoclase
        videoclase = None
        if file:
            videoclase = crud.create_videoclase(
                db,
                schemas.VideoclaseCreate(
                    title=titulo,
                    especialidad=especialidad,
                    sistema=sistema,
                    tema=tema,
                    url=video_url,
                    file_path=video_file_path,
                    thumbnail_url=thumbnail_url
                )
            )

        # Create quiz if provided
        if quiz:
            try:
                quiz_data = json.loads(quiz)
                # Convert quiz data to proper format
                preguntas = []
                for q in quiz_data.get('preguntas', []):
                    answers = [schemas.Respuesta(**answer) for answer in q.get('answers', [])]
                    preguntas.append(schemas.PreguntaCreate(
                        text=q.get('text', ''),
                        answers=answers,
                        explanation=q.get('explanation')
                    ))
                
                crud.create_cuestionario(
                    db,
                    schemas.CuestionarioCreate(
                        title=f"Cuestionario - {titulo}",
                        especialidad=especialidad,
                        sistema=sistema,
                        tema=tema,
                        preguntas=preguntas
                    )
                )
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Formato de cuestionario JSON inválido")

        return {
            "success": True,
            "message": "Videoclase subida exitosamente",
            "videoclase": schemas.VideoclaseOut.model_validate(videoclase) if videoclase else None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/videoclases", response_model=list[schemas.VideoclaseOut])
def list_videoclases(db: Session = Depends(get_db_session)): 
    return crud.list_videoclases(db)


@router.get("/videos-cortos", response_model=list[schemas.VideoCortoOut])
def list_videos_cortos(db: Session = Depends(get_db_session)): 
    return crud.list_videos_cortos(db)

@router.put("/videos/{videoclase_id}")
async def update_videoclase(
    videoclase_id: int,
    file: Optional[UploadFile] = File(None),
    thumbnail: Optional[UploadFile] = File(None),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    db: Session = Depends(get_db_session)
):
    try:
        videoclase = crud.get_videoclase(db, videoclase_id)
        if not videoclase:
            raise HTTPException(status_code=404, detail="Videoclase no encontrada")

        # Handle video update
        video_url = videoclase.url
        video_file_path = videoclase.file_path
        thumbnail_url = videoclase.thumbnail_url

        if file:
            # Delete old video file from Google Cloud Storage
            if videoclase.file_path:
                try:
                    await delete_file_from_gcs(videoclase.file_path)
                except Exception:
                    print(f"Error deleting old video file: {videoclase.file_path}")
                    pass

            # Upload new video
            allowed_extensions = {"mp4", "webm", "mov", "avi", "mkv"}
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in allowed_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de video no permitido")

            video_file_path = f"videoclases/{especialidad}/{tema}/{datetime.utcnow().timestamp()}.{file_extension}"
            video_url = await upload_file_to_gcs(
                file=file,
                file_path=video_file_path,
                content_type=f"video/{file_extension}"
            )

        # Handle thumbnail update
        if thumbnail:
            allowed_image_extensions = {"jpg", "jpeg", "png", "webp"}
            thumbnail_extension = thumbnail.filename.split(".")[-1].lower()
            if thumbnail_extension not in allowed_image_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de thumbnail no permitido")

            thumbnail_file_path = f"thumbnails/{especialidad}/{tema}/{datetime.utcnow().timestamp()}.{thumbnail_extension}"
            thumbnail_url = await upload_file_to_gcs(
                file=thumbnail,
                file_path=thumbnail_file_path,
                content_type=f"image/{thumbnail_extension}"
            )

        # Update videoclase
        updated_videoclase = crud.update_videoclase(
            db,
            videoclase_id,
            schemas.VideoclaseUpdate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                url=video_url,
                file_path=video_file_path,
                thumbnail_url=thumbnail_url
            )
        )

        return {
            "success": True,
            "message": "Videoclase actualizada exitosamente",
            "videoclase": schemas.VideoclaseOut.model_validate(updated_videoclase)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/videos/{videoclase_id}")
async def delete_videoclase(videoclase_id: int, db: Session = Depends(get_db_session)):
    try:
        videoclase = crud.get_videoclase(db, videoclase_id)
        if not videoclase:
            raise HTTPException(status_code=404, detail="Videoclase no encontrada")

        # Delete associated files from Google Cloud Storage
        if videoclase.file_path:
            try:
                await delete_file_from_gcs(videoclase.file_path)
            except Exception:
                print(f"Error deleting video file: {videoclase.file_path}")
                pass

        # Delete videoclase
        crud.delete_videoclase(db, videoclase_id)

        return {
            "success": True,
            "message": "Videoclase eliminada exitosamente"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Videos Cortos Endpoints
@router.post("/upload-video-corto")
async def upload_video_corto(
    file: Optional[UploadFile] = File(None),
    thumbnail: Optional[UploadFile] = File(None),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    description: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    try:
        # Handle video upload
        video_url = None
        video_file_path = None
        if file:
            # Validate video file type
            allowed_video_extensions = {"mp4", "webm", "mov", "avi", "mkv"}
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in allowed_video_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de video no permitido")

            # Upload video to Google Cloud Storage
            video_file_path = f"videos-cortos/{especialidad}/{sistema}/{tema}/{datetime.utcnow().timestamp()}.{file_extension}"
            video_url = await upload_file_to_gcs(
                file=file,
                file_path=video_file_path,
                content_type=f"video/{file_extension}"
            )

        # Handle thumbnail upload
        thumbnail_url = None
        if thumbnail:
            # Validate thumbnail file type
            allowed_image_extensions = {"jpg", "jpeg", "png", "webp"}
            thumbnail_extension = thumbnail.filename.split(".")[-1].lower()
            if thumbnail_extension not in allowed_image_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de thumbnail no permitido")

            # Upload thumbnail to Google Cloud Storage
            thumbnail_file_path = f"thumbnails-cortos/{especialidad}/{sistema}/{tema}/{datetime.utcnow().timestamp()}.{thumbnail_extension}"
            thumbnail_url = await upload_file_to_gcs(
                file=thumbnail,
                file_path=thumbnail_file_path,
                content_type=f"image/{thumbnail_extension}"
            )

        # Create video corto
        video_corto = None
        if file:
            video_corto = crud.create_video_corto(
                db,
                schemas.VideoCortoCreate(
                    title=titulo,
                    especialidad=especialidad,
                    sistema=sistema,
                    tema=tema,
                    url=video_url,
                    file_path=video_file_path,
                    thumbnail_url=thumbnail_url,
                    description=description
                )
            )

        return {
            "success": True,
            "message": "Video corto subido exitosamente",
            "video_corto": schemas.VideoCortoOut.model_validate(video_corto) if video_corto else None
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/videos-cortos/{video_corto_id}")
async def update_video_corto(
    video_corto_id: int,
    file: Optional[UploadFile] = File(None),
    thumbnail: Optional[UploadFile] = File(None),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    description: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    try:
        video_corto = crud.get_video_corto(db, video_corto_id)
        if not video_corto:
            raise HTTPException(status_code=404, detail="Video corto no encontrado")

        # Handle video update
        video_url = video_corto.url
        video_file_path = video_corto.file_path
        thumbnail_url = video_corto.thumbnail_url

        if file:
            # Delete old video file from Google Cloud Storage
            if video_corto.file_path:
                try:
                    await delete_file_from_gcs(video_corto.file_path)
                except Exception:
                    print(f"Error deleting old video file: {video_corto.file_path}")
                    pass

            # Upload new video
            allowed_extensions = {"mp4", "webm", "mov", "avi", "mkv"}
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in allowed_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de video no permitido")

            video_file_path = f"videos-cortos/{especialidad}/{sistema}/{tema}/{datetime.utcnow().timestamp()}.{file_extension}"
            video_url = await upload_file_to_gcs(
                file=file,
                file_path=video_file_path,
                content_type=f"video/{file_extension}"
            )

        # Handle thumbnail update
        if thumbnail:
            allowed_image_extensions = {"jpg", "jpeg", "png", "webp"}
            thumbnail_extension = thumbnail.filename.split(".")[-1].lower()
            if thumbnail_extension not in allowed_image_extensions:
                raise HTTPException(status_code=400, detail="Tipo de archivo de thumbnail no permitido")

            thumbnail_file_path = f"thumbnails-cortos/{especialidad}/{sistema}/{tema}/{datetime.utcnow().timestamp()}.{thumbnail_extension}"
            thumbnail_url = await upload_file_to_gcs(
                file=thumbnail,
                file_path=thumbnail_file_path,
                content_type=f"image/{thumbnail_extension}"
            )

        # Update video corto
        updated_video_corto = crud.update_video_corto(
            db,
            video_corto_id,
            schemas.VideoCortoUpdate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                url=video_url,
                file_path=video_file_path,
                thumbnail_url=thumbnail_url,
                description=description
            )
        )

        return {
            "success": True,
            "message": "Video corto actualizado exitosamente",
            "video_corto": schemas.VideoCortoOut.model_validate(updated_video_corto)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/videos-cortos/{video_corto_id}")
async def delete_video_corto(video_corto_id: int, db: Session = Depends(get_db_session)):
    try:
        video_corto = crud.get_video_corto(db, video_corto_id)
        if not video_corto:
            raise HTTPException(status_code=404, detail="Video corto no encontrado")

        # Delete associated files from Google Cloud Storage
        if video_corto.file_path:
            try:
                await delete_file_from_gcs(video_corto.file_path)
            except Exception:
                print(f"Error deleting video file: {video_corto.file_path}")
                pass

        # Delete video corto
        crud.delete_video_corto(db, video_corto_id)

        return {
            "success": True,
            "message": "Video corto eliminado exitosamente"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clinical-notes")
async def upload_clinical_notes(
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    content: str = Form(...),
    images: List[UploadFile] = File(default=[]),
    image_ids: List[str] = Form(default=[]),
    db: Session = Depends(get_db_session)
):
    try:
        # Upload images to Google Cloud Storage
        images_data = []
        
        for i, image_file in enumerate(images):
            if image_file.size > 0:  # Check if file is not empty
                # Validate image file type
                allowed_extensions = {"jpg", "jpeg", "png", "webp", "gif"}
                file_extension = image_file.filename.split(".")[-1].lower()
                if file_extension not in allowed_extensions:
                    raise HTTPException(status_code=400, detail=f"Tipo de archivo no permitido: {file_extension}")

                # Generate file path
                image_id = image_ids[i] if i < len(image_ids) else f"img_{i}_{datetime.utcnow().timestamp()}"
                file_path = f"clinical-notes/{especialidad}/{tema}/{image_id}.{file_extension}"
                
                # Upload to Google Cloud Storage
                image_url = await upload_file_to_gcs(
                    file=image_file,
                    file_path=file_path,
                    content_type=f"image/{file_extension}"
                )
                
                images_data.append({
                    "id": image_id,
                    "url": image_url,
                    "file_path": file_path,
                    "filename": image_file.filename
                })

        # Create clinical notes
        nota_clinica = crud.create_nota_clinica(
            db,
            schemas.NotaClinicaCreate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                content=content
            ),
            images_data
        )

        return {
            "success": True,
            "message": "Notas clínicas subidas exitosamente",
            "nota_clinica": {
                "id": nota_clinica.id,
                "title": titulo,
                "especialidad": especialidad,
                "tema": tema,
                "content": content,
                "images": images_data,
                "created_at": nota_clinica.created_at
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/clinical-notes", response_model=List[schemas.NotaClinicaOut])
def list_clinical_notes(db: Session = Depends(get_db_session)):
    return crud.list_notas_clinicas(db)


@router.get("/clinical-notes/{nota_id}", response_model=schemas.NotaClinicaOut)
def get_clinical_note(nota_id: int, db: Session = Depends(get_db_session)):
    nota = crud.get_nota_clinica(db, nota_id)
    if not nota:
        raise HTTPException(status_code=404, detail="Nota clínica no encontrada")
    return nota


@router.post("/clinical-cases")
async def upload_clinical_case(
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    descripcion: str = Form(...),
    preguntas: str = Form(...),  # JSON string
    case_images: List[UploadFile] = File(default=[]),
    case_image_ids: List[str] = Form(default=[]),
    question_images: List[UploadFile] = File(default=[]),
    question_image_ids: List[str] = Form(default=[]),
    option_images: List[UploadFile] = File(default=[]),
    option_image_ids: List[str] = Form(default=[]),
    db: Session = Depends(get_db_session)
):
    try:
        # Parse questions data
        try:
            preguntas_data = json.loads(preguntas)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Formato de preguntas JSON inválido")

        # Upload case description images
        case_images_data = []
        for i, image_file in enumerate(case_images):
            if image_file.size > 0:
                allowed_extensions = {"jpg", "jpeg", "png", "webp", "gif"}
                file_extension = image_file.filename.split(".")[-1].lower()
                if file_extension not in allowed_extensions:
                    raise HTTPException(status_code=400, detail=f"Tipo de archivo no permitido: {file_extension}")

                image_id = case_image_ids[i] if i < len(case_image_ids) else f"case_img_{i}_{datetime.utcnow().timestamp()}"
                file_path = f"clinical-cases/{especialidad}/{tema}/case/{image_id}.{file_extension}"
                
                image_url = await upload_file_to_gcs(
                    file=image_file,
                    file_path=file_path,
                    content_type=f"image/{file_extension}"
                )
                
                case_images_data.append({
                    "id": image_id,
                    "url": image_url,
                    "file_path": file_path,
                    "filename": image_file.filename
                })

        # Create image lookup dictionaries
        question_images_dict = {}
        option_images_dict = {}

        # Upload question images
        for i, image_file in enumerate(question_images):
            if image_file.size > 0:
                allowed_extensions = {"jpg", "jpeg", "png", "webp", "gif"}
                file_extension = image_file.filename.split(".")[-1].lower()
                if file_extension not in allowed_extensions:
                    raise HTTPException(status_code=400, detail=f"Tipo de archivo no permitido: {file_extension}")

                image_id = question_image_ids[i] if i < len(question_image_ids) else f"q_img_{i}_{datetime.utcnow().timestamp()}"
                file_path = f"clinical-cases/{especialidad}/{tema}/questions/{image_id}.{file_extension}"
                
                image_url = await upload_file_to_gcs(
                    file=image_file,
                    file_path=file_path,
                    content_type=f"image/{file_extension}"
                )
                
                question_images_dict[image_id] = {
                    "url": image_url,
                    "file_path": file_path
                }

        # Upload option images
        for i, image_file in enumerate(option_images):
            if image_file.size > 0:
                allowed_extensions = {"jpg", "jpeg", "png", "webp", "gif"}
                file_extension = image_file.filename.split(".")[-1].lower()
                if file_extension not in allowed_extensions:
                    raise HTTPException(status_code=400, detail=f"Tipo de archivo no permitido: {file_extension}")

                image_id = option_image_ids[i] if i < len(option_image_ids) else f"o_img_{i}_{datetime.utcnow().timestamp()}"
                file_path = f"clinical-cases/{especialidad}/{tema}/options/{image_id}.{file_extension}"
                
                image_url = await upload_file_to_gcs(
                    file=image_file,
                    file_path=file_path,
                    content_type=f"image/{file_extension}"
                )
                
                option_images_dict[image_id] = {
                    "url": image_url,
                    "file_path": file_path
                }

        # Process questions data and add image URLs
        processed_preguntas = []
        for pregunta_data in preguntas_data:
            pregunta_image_id = pregunta_data.get('imagen_id')
            pregunta_image_info = question_images_dict.get(pregunta_image_id) if pregunta_image_id else None
            
            processed_opciones = []
            for opcion_data in pregunta_data.get('opciones', []):
                opcion_image_id = opcion_data.get('imagen_id')
                opcion_image_info = option_images_dict.get(opcion_image_id) if opcion_image_id else None
                
                processed_opciones.append({
                    'texto': opcion_data['texto'],
                    'es_correcta': opcion_data['es_correcta'],
                    'explicacion': opcion_data['explicacion'],
                    'imagen_url': opcion_image_info['url'] if opcion_image_info else None,
                    'imagen_path': opcion_image_info['file_path'] if opcion_image_info else None
                })
            
            processed_preguntas.append({
                'texto': pregunta_data['texto'],
                'imagen_url': pregunta_image_info['url'] if pregunta_image_info else None,
                'imagen_path': pregunta_image_info['file_path'] if pregunta_image_info else None,
                'opciones': processed_opciones
            })

        # Create clinical case
        caso_clinico = crud.create_caso_clinico(
            db,
            schemas.CasoClinicoCreate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                descripcion=descripcion,
                preguntas=[]  # We'll handle preguntas separately
            ),
            case_images_data,
            processed_preguntas
        )

        return {
            "success": True,
            "message": "Caso clínico subido exitosamente",
            "caso_clinico": {
                "id": caso_clinico.id,
                "title": titulo,
                "especialidad": especialidad,
                "tema": tema,
                "descripcion": descripcion,
                "images": case_images_data,
                "preguntas": processed_preguntas,
                "created_at": caso_clinico.created_at
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/clinical-cases", response_model=List[schemas.CasoClinicoOut])
def list_clinical_cases(db: Session = Depends(get_db_session)):
    return crud.list_casos_clinicos(db)


@router.get("/clinical-cases/{caso_id}", response_model=schemas.CasoClinicoOut)
def get_clinical_case(caso_id: int, db: Session = Depends(get_db_session)):
    caso = crud.get_caso_clinico(db, caso_id)
    if not caso:
        raise HTTPException(status_code=404, detail="Caso clínico no encontrado")
    return caso


@router.post("/clinical-cases/json")
async def upload_clinical_cases_json(
    json_content: str = Form(...),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    """Upload clinical cases from JSON content"""
    try:
        # Parse JSON content
        try:
            json_data = json.loads(json_content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Formato JSON inválido")

        # Validate JSON structure
        if not isinstance(json_data, list):
            raise HTTPException(status_code=400, detail="El JSON debe contener una lista de casos clínicos")

        # Convert to schema format
        casos_json = []
        for caso_data in json_data:
            # Validate required fields
            required_fields = ['id', 'caso_clinico', 'pregunta', 'opciones', 'respuesta_correcta', 'explicacion_general']
            for field in required_fields:
                if field not in caso_data:
                    raise HTTPException(status_code=400, detail=f"Campo requerido faltante: {field}")

            # Validate opciones structure
            if not isinstance(caso_data['opciones'], list):
                raise HTTPException(status_code=400, detail="Las opciones deben ser una lista")

            opciones_json = []
            for opcion in caso_data['opciones']:
                if 'opcion' not in opcion or 'es_correcta' not in opcion or 'explicacion' not in opcion:
                    raise HTTPException(status_code=400, detail="Cada opción debe tener 'opcion', 'es_correcta' y 'explicacion'")

                opciones_json.append(schemas.OpcionJsonCreate(
                    opcion=opcion['opcion'],
                    es_correcta=opcion['es_correcta'],
                    explicacion=opcion['explicacion']
                ))

            casos_json.append(schemas.CasoClinicoJsonCreate(
                id=caso_data['id'],
                caso_clinico=caso_data['caso_clinico'],
                pregunta=caso_data['pregunta'],
                opciones=opciones_json,
                respuesta_correcta=caso_data['respuesta_correcta'],
                explicacion_general=caso_data['explicacion_general']
            ))

        # Create upload payload
        upload_payload = schemas.CasoClinicoJsonUpload(
            casos=casos_json,
            especialidad=especialidad,
            sistema=sistema,
            tema=tema,
            title=titulo
        )

        # Create clinical cases
        created_casos = crud.create_casos_clinicos_from_json(db, upload_payload)

        return {
            "success": True,
            "message": f"Se crearon {len(created_casos)} casos clínicos exitosamente",
            "casos_creados": len(created_casos),
            "casos_clinicos": [
                {
                    "id": caso.id,
                    "title": caso.title,
                    "especialidad": caso.especialidad,
                    "tema": caso.tema,
                    "created_at": caso.created_at
                }
                for caso in created_casos
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/clinical-cases/{caso_id}")
async def update_clinical_case(
    caso_id: int,
    title: Optional[str] = Form(None),
    descripcion: Optional[str] = Form(None),
    preguntas: Optional[str] = Form(None),  # JSON string
    new_case_images: List[UploadFile] = File(default=[]),
    new_case_image_ids: List[str] = Form(default=[]),
    new_question_images: List[UploadFile] = File(default=[]),
    new_question_image_ids: List[str] = Form(default=[]),
    new_question_image_indices: List[str] = Form(default=[]),
    new_option_images: List[UploadFile] = File(default=[]),
    new_option_image_ids: List[str] = Form(default=[]),
    new_option_image_keys: List[str] = Form(default=[]),
    db: Session = Depends(get_db_session)
):
    """Update an existing clinical case"""
    try:
        # Get existing case
        caso = crud.get_caso_clinico(db, caso_id)
        if not caso:
            raise HTTPException(status_code=404, detail="Caso clínico no encontrado")

        # Prepare update data
        update_data = {}

        if title is not None:
            update_data['title'] = title

        if descripcion is not None:
            update_data['descripcion'] = descripcion

        # Handle new case images
        case_images_data = list(caso.images) if caso.images else []
        if new_case_images:
            for i, image_file in enumerate(new_case_images):
                if i < len(new_case_image_ids):
                    image_id = new_case_image_ids[i]

                    # Upload to GCS
                    file_path, public_url = await upload_file_to_gcs(
                        image_file,
                        f"clinical-cases/{caso_id}/case-images/{image_id}_{image_file.filename}"
                    )

                    case_images_data.append({
                        'id': image_id,
                        'filename': image_file.filename,
                        'url': public_url,
                        'file_path': file_path
                    })

        update_data['images'] = case_images_data

        if preguntas is not None:
            try:
                preguntas_data = json.loads(preguntas)

                # Handle new question and option images
                question_image_map = {}
                if new_question_images:
                    for i, image_file in enumerate(new_question_images):
                        if i < len(new_question_image_ids) and i < len(new_question_image_indices):
                            image_id = new_question_image_ids[i]
                            question_index = int(new_question_image_indices[i])

                            # Upload to GCS
                            file_path, public_url = await upload_file_to_gcs(
                                image_file,
                                f"clinical-cases/{caso_id}/questions/{question_index}_{image_id}_{image_file.filename}"
                            )

                            question_image_map[question_index] = {
                                'url': public_url,
                                'file_path': file_path
                            }

                option_image_map = {}
                if new_option_images:
                    for i, image_file in enumerate(new_option_images):
                        if i < len(new_option_image_ids) and i < len(new_option_image_keys):
                            image_id = new_option_image_ids[i]
                            key = new_option_image_keys[i]

                            # Upload to GCS
                            file_path, public_url = await upload_file_to_gcs(
                                image_file,
                                f"clinical-cases/{caso_id}/options/{key}_{image_id}_{image_file.filename}"
                            )

                            option_image_map[key] = {
                                'url': public_url,
                                'file_path': file_path
                            }

                # Update preguntas with new image URLs
                for q_index, pregunta in enumerate(preguntas_data):
                    if q_index in question_image_map:
                        pregunta['imagen_url'] = question_image_map[q_index]['url']
                        pregunta['imagen_path'] = question_image_map[q_index]['file_path']

                    if 'opciones' in pregunta:
                        for o_index, opcion in enumerate(pregunta['opciones']):
                            key = f"{q_index}-{o_index}"
                            if key in option_image_map:
                                opcion['imagen_url'] = option_image_map[key]['url']
                                opcion['imagen_path'] = option_image_map[key]['file_path']

                update_data['preguntas'] = preguntas_data
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Formato de preguntas JSON inválido")

        # Update the case
        updated_caso = crud.update_caso_clinico(
            db,
            caso_id,
            schemas.CasoClinicoUpdate(**update_data)
        )

        if not updated_caso:
            raise HTTPException(status_code=404, detail="Caso clínico no encontrado")

        return {
            "success": True,
            "message": "Caso clínico actualizado exitosamente",
            "caso_clinico": {
                "id": updated_caso.id,
                "title": updated_caso.title,
                "especialidad": updated_caso.especialidad,
                "tema": updated_caso.tema,
                "descripcion": updated_caso.descripcion,
                "images": updated_caso.images,
                "preguntas": updated_caso.preguntas,
                "created_at": updated_caso.created_at
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/clinical-cases/{caso_id}")
async def delete_clinical_case(caso_id: int, db: Session = Depends(get_db_session)):
    """Delete a clinical case"""
    try:
        caso = crud.get_caso_clinico(db, caso_id)
        if not caso:
            raise HTTPException(status_code=404, detail="Caso clínico no encontrado")

        # Delete associated images from Google Cloud Storage if they exist
        if caso.images:
            for image in caso.images:
                if 'file_path' in image:
                    try:
                        await delete_file_from_gcs(image['file_path'])
                    except Exception:
                        print(f"Error deleting image file: {image['file_path']}")
                        pass

        # Delete case
        success = crud.delete_caso_clinico(db, caso_id)
        if not success:
            raise HTTPException(status_code=404, detail="Caso clínico no encontrado")

        return {
            "success": True,
            "message": "Caso clínico eliminado exitosamente"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cuestionarios/json")
async def upload_cuestionarios_json(
    json_content: str = Form(...),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    """Upload questionnaires from JSON content (new structure)"""
    try:
        # Parse JSON content
        try:
            json_data = json.loads(json_content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Formato JSON inválido")

        # Validate JSON structure
        if not isinstance(json_data, list):
            raise HTTPException(status_code=400, detail="El JSON debe contener una lista de cuestionarios")

        # Convert to schema format
        cuestionarios_json = []
        for cuestionario_data in json_data:
            # Validate required fields
            required_fields = ['id', 'pregunta', 'opciones', 'respuesta_correcta', 'explicacion_general']
            for field in required_fields:
                if field not in cuestionario_data:
                    raise HTTPException(status_code=400, detail=f"Campo requerido faltante: {field}")

            # Validate opciones structure
            if not isinstance(cuestionario_data['opciones'], list):
                raise HTTPException(status_code=400, detail="Las opciones deben ser una lista")

            opciones_json = []
            for opcion in cuestionario_data['opciones']:
                if 'opcion' not in opcion or 'es_correcta' not in opcion or 'explicacion' not in opcion:
                    raise HTTPException(status_code=400, detail="Cada opción debe tener 'opcion', 'es_correcta' y 'explicacion'")

                opciones_json.append(schemas.OpcionCuestionarioJsonCreate(
                    opcion=opcion['opcion'],
                    es_correcta=opcion['es_correcta'],
                    explicacion=opcion['explicacion']
                ))

            cuestionarios_json.append(schemas.CuestionarioJsonCreate(
                id=cuestionario_data['id'],
                pregunta=cuestionario_data['pregunta'],
                opciones=opciones_json,
                respuesta_correcta=cuestionario_data['respuesta_correcta'],
                explicacion_general=cuestionario_data['explicacion_general']
            ))

        # Create upload payload
        upload_payload = schemas.CuestionarioJsonUpload(
            cuestionarios=cuestionarios_json,
            especialidad=especialidad,
            sistema=sistema,
            tema=tema,
            title=titulo
        )

        # Create questionnaires
        created_cuestionarios = crud.create_cuestionarios_from_json(db, upload_payload)

        return {
            "success": True,
            "message": f"Se crearon {len(created_cuestionarios)} cuestionarios exitosamente",
            "cuestionarios_creados": len(created_cuestionarios),
            "cuestionarios": [
                {
                    "id": cuestionario.id,
                    "title": cuestionario.title,
                    "especialidad": cuestionario.especialidad,
                    "tema": cuestionario.tema,
                    "created_at": cuestionario.created_at
                }
                for cuestionario in created_cuestionarios
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/cuestionarios/{cuestionario_id}")
async def update_cuestionario(
    cuestionario_id: int,
    payload: schemas.CuestionarioUpdate,
    db: Session = Depends(get_db_session)
):
    """Update a questionnaire"""
    cuestionario = crud.update_cuestionario(db, cuestionario_id, payload)
    if not cuestionario:
        raise HTTPException(status_code=404, detail="Cuestionario no encontrado")
    return cuestionario


@router.delete("/cuestionarios/{cuestionario_id}")
async def delete_cuestionario(
    cuestionario_id: int,
    db: Session = Depends(get_db_session)
):
    """Delete a questionnaire"""
    success = crud.delete_cuestionario(db, cuestionario_id)
    if not success:
        raise HTTPException(status_code=404, detail="Cuestionario no encontrado")
    return {"success": True, "message": "Cuestionario eliminado exitosamente"}





@router.post("/cuestionarios")
async def create_cuestionario(
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    preguntas: str = Form(...),  # JSON string
    db: Session = Depends(get_db_session)
):
    try:
        # Parse questions data
        try:
            preguntas_data = json.loads(preguntas)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Formato de preguntas JSON inválido")

        # Convert to proper format
        preguntas_list = []
        for q in preguntas_data:
            answers = [schemas.Respuesta(**answer) for answer in q.get('answers', [])]
            preguntas_list.append(schemas.PreguntaCreate(
                text=q.get('text', ''),
                answers=answers,
                explanation=q.get('explanation')
            ))

        # Create cuestionario
        cuestionario = crud.create_cuestionario(
            db,
            schemas.CuestionarioCreate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                preguntas=preguntas_list
            )
        )

        return {
            "success": True,
            "message": "Cuestionario creado exitosamente",
            "cuestionario": schemas.CuestionarioOut.model_validate(cuestionario)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cuestionarios", response_model=List[schemas.CuestionarioOut])
def list_cuestionarios(db: Session = Depends(get_db_session)):
    return crud.list_cuestionarios(db)


@router.get("/cuestionarios/{cuestionario_id}", response_model=schemas.CuestionarioOut)
def get_cuestionario(cuestionario_id: int, db: Session = Depends(get_db_session)):
    cuestionario = crud.get_cuestionario(db, cuestionario_id)
    if not cuestionario:
        raise HTTPException(status_code=404, detail="Cuestionario no encontrado")
    return cuestionario





@router.delete("/cuestionarios/{cuestionario_id}")
def delete_cuestionario(cuestionario_id: int, db: Session = Depends(get_db_session)):
    try:
        success = crud.delete_cuestionario(db, cuestionario_id)
        if not success:
            raise HTTPException(status_code=404, detail="Cuestionario no encontrado")

        return {
            "success": True,
            "message": "Cuestionario eliminado exitosamente"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Utility endpoints for especialidades and temas
@router.get("/especialidades")
def get_especialidades(db: Session = Depends(get_db_session)):
    """Get all unique especialidades across all content types"""
    return {
        "especialidades": crud.get_unique_especialidades(db)
    }


@router.get("/especialidades/{especialidad}/sistemas")
def get_sistemas_by_especialidad(especialidad: str, db: Session = Depends(get_db_session)):
    """Get all unique sistemas for a specific especialidad"""
    return {
        "sistemas": crud.get_sistemas_by_especialidad(db, especialidad)
    }


@router.get("/especialidades/{especialidad}/sistemas/{sistema}/temas")
def get_temas_by_especialidad_sistema(especialidad: str, sistema: str, db: Session = Depends(get_db_session)):
    """Get all unique temas for a specific especialidad and sistema"""
    return {
        "temas": crud.get_temas_by_especialidad_sistema(db, especialidad, sistema)
    }


@router.post("/flashcards/json")
async def upload_flashcards_json(
    json_content: str = Form(...),
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    """Upload flashcards from JSON content"""
    try:
        # Parse JSON content
        try:
            json_data = json.loads(json_content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Formato JSON inválido")

        # Validate JSON structure
        if not isinstance(json_data, list):
            raise HTTPException(status_code=400, detail="El JSON debe contener una lista de flashcards")

        # Convert to schema format
        flashcards_json = []
        for flashcard_data in json_data:
            # Validate required fields
            required_fields = ['id', 'question', 'answer']
            for field in required_fields:
                if field not in flashcard_data:
                    raise HTTPException(status_code=400, detail=f"Campo requerido faltante: {field}")

            flashcards_json.append(schemas.FlashcardJsonCreate(
                id=flashcard_data['id'],
                question=flashcard_data['question'],
                answer=flashcard_data['answer'],
                explanation=flashcard_data.get('explanation'),
                difficulty=flashcard_data.get('difficulty'),
                category=flashcard_data.get('category'),
                tags=flashcard_data.get('tags', [])
            ))

        # Create upload payload
        upload_payload = schemas.FlashcardJsonUpload(
            flashcards=flashcards_json,
            especialidad=especialidad,
            sistema=sistema,
            tema=tema,
            title=titulo
        )

        # Create flashcards
        created_flashcards = crud.create_flashcards_from_json(db, upload_payload)

        return {
            "success": True,
            "message": f"Se crearon {len(created_flashcards)} flashcards exitosamente",
            "flashcards_creadas": len(created_flashcards),
            "flashcards": [
                {
                    "id": flashcard.id,
                    "title": flashcard.title,
                    "especialidad": flashcard.especialidad,
                    "tema": flashcard.tema,
                    "created_at": flashcard.created_at
                }
                for flashcard in created_flashcards
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/flashcards")
async def create_flashcard(
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    question: str = Form(...),
    answer: str = Form(...),
    explanation: Optional[str] = Form(None),
    difficulty: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),  # JSON string of tags
    db: Session = Depends(get_db_session)
):
    try:
        # Parse tags if provided
        tags_list = []
        if tags:
            try:
                tags_list = json.loads(tags)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Formato de tags JSON inválido")

        # Create flashcard
        flashcard = crud.create_flashcard(
            db,
            schemas.FlashcardCreate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                question=question,
                answer=answer,
                explanation=explanation,
                difficulty=difficulty,
                category=category,
                tags=tags_list
            )
        )

        return {
            "success": True,
            "message": "Flashcard creada exitosamente",
            "flashcard": schemas.FlashcardOut.model_validate(flashcard)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/flashcards", response_model=List[schemas.FlashcardOut])
def list_flashcards(db: Session = Depends(get_db_session)):
    return crud.list_flashcards(db)


@router.get("/flashcards/{flashcard_id}", response_model=schemas.FlashcardOut)
def get_flashcard(flashcard_id: int, db: Session = Depends(get_db_session)):
    flashcard = crud.get_flashcard(db, flashcard_id)
    if not flashcard:
        raise HTTPException(status_code=404, detail="Flashcard no encontrada")
    return flashcard


@router.put("/flashcards/{flashcard_id}")
async def update_flashcard(
    flashcard_id: int,
    especialidad: str = Form(...),
    sistema: str = Form(...),
    tema: str = Form(...),
    titulo: str = Form(...),
    question: str = Form(...),
    answer: str = Form(...),
    explanation: Optional[str] = Form(None),
    difficulty: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    tags: Optional[str] = Form(None),
    db: Session = Depends(get_db_session)
):
    try:
        # Parse tags if provided
        tags_list = []
        if tags:
            try:
                tags_list = json.loads(tags)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Formato de tags JSON inválido")

        updated_flashcard = crud.update_flashcard(
            db,
            flashcard_id,
            schemas.FlashcardUpdate(
                title=titulo,
                especialidad=especialidad,
                sistema=sistema,
                tema=tema,
                question=question,
                answer=answer,
                explanation=explanation,
                difficulty=difficulty,
                category=category,
                tags=tags_list
            )
        )
        
        if not updated_flashcard:
            raise HTTPException(status_code=404, detail="Flashcard no encontrada")

        return {
            "success": True,
            "message": "Flashcard actualizada exitosamente",
            "flashcard": schemas.FlashcardOut.model_validate(updated_flashcard)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/flashcards/{flashcard_id}")
def delete_flashcard(flashcard_id: int, db: Session = Depends(get_db_session)):
    try:
        success = crud.delete_flashcard(db, flashcard_id)
        if not success:
            raise HTTPException(status_code=404, detail="Flashcard no encontrada")

        return {
            "success": True,
            "message": "Flashcard eliminada exitosamente"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/content/{especialidad}/{sistema}/{tema}")
def get_content_by_especialidad_sistema_tema(especialidad: str, sistema: str, tema: str, db: Session = Depends(get_db_session)):
    """Get all content types for a specific especialidad, sistema and tema"""
    content = crud.list_content_by_especialidad_sistema_tema(db, especialidad, sistema, tema)
    
    return {
        "especialidad": especialidad,
        "sistema": sistema,
        "tema": tema,
        "videoclases": [schemas.VideoclaseOut.model_validate(v) for v in content["videoclases"]],
        "videos_cortos": [schemas.VideoCortoOut.model_validate(v) for v in content["videos_cortos"]],
        "notas_clinicas": [schemas.NotaClinicaOut.model_validate(n) for n in content["notas_clinicas"]],
        "casos_clinicos": [schemas.CasoClinicoOut.model_validate(c) for c in content["casos_clinicos"]],
        "cuestionarios": [schemas.CuestionarioOut.model_validate(q) for q in content["cuestionarios"]],
        "flashcards": [schemas.FlashcardOut.model_validate(f) for f in content["flashcards"]]
    }