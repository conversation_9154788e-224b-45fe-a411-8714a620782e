"""create simplified content tables

Revision ID: daf59bc87b6d
Revises: 573bdcbf5938
Create Date: 2025-09-13 23:45:35.386169

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'daf59bc87b6d'
down_revision: Union[str, Sequence[str], None] = '573bdcbf5938'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create new simplified tables first
    op.create_table('casos_clinicos_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('sistema', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('descripcion', sa.Text(), nullable=False),
    sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('preguntas', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_casos_clinicos_data_id'), 'casos_clinicos_data', ['id'], unique=False)
    
    op.create_table('cuestionarios_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('sistema', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('preguntas', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cuestionarios_data_id'), 'cuestionarios_data', ['id'], unique=False)
    
    op.create_table('flashcards_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('sistema', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('question', sa.Text(), nullable=False),
    sa.Column('answer', sa.Text(), nullable=False),
    sa.Column('explanation', sa.Text(), nullable=True),
    sa.Column('difficulty', sa.String(length=50), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_flashcards_data_id'), 'flashcards_data', ['id'], unique=False)
    
    op.create_table('notas_clinicas_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('sistema', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notas_clinicas_data_id'), 'notas_clinicas_data', ['id'], unique=False)
    
    op.create_table('videoclases_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('sistema', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('url', sa.String(length=512), nullable=True),
    sa.Column('file_path', sa.String(length=512), nullable=True),
    sa.Column('thumbnail_url', sa.String(length=512), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_videoclases_data_id'), 'videoclases_data', ['id'], unique=False)
    
    op.create_table('videos_cortos_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('especialidad', sa.String(length=255), nullable=False),
    sa.Column('sistema', sa.String(length=255), nullable=False),
    sa.Column('tema', sa.String(length=255), nullable=False),
    sa.Column('url', sa.String(length=512), nullable=True),
    sa.Column('file_path', sa.String(length=512), nullable=True),
    sa.Column('thumbnail_url', sa.String(length=512), nullable=True),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_videos_cortos_data_id'), 'videos_cortos_data', ['id'], unique=False)

    # Migrate existing data from content_videoclases to videoclases_data
    op.execute("""
        INSERT INTO videoclases_data (title, especialidad, sistema, tema, url, file_path, created_at)
        SELECT 
            l.title,
            l.especialidad,
            'General' as sistema,  -- Default sistema since old structure didn't have it
            l.tema,
            v.url,
            v.file_path,
            v.created_at
        FROM content_videoclases v
        JOIN content_lecciones l ON v.leccion_id = l.id
    """)

    # Migrate existing data from content_notas_clinicas to notas_clinicas_data
    op.execute("""
        INSERT INTO notas_clinicas_data (title, especialidad, sistema, tema, content, created_at)
        SELECT 
            l.title,
            l.especialidad,
            'General' as sistema,  -- Default sistema since old structure didn't have it
            l.tema,
            n.content,
            n.created_at
        FROM content_notas_clinicas n
        JOIN content_lecciones l ON n.leccion_id = l.id
    """)

    # Now drop the old tables in the correct order (children first, then parents)
    # Drop tables with foreign keys first
    op.drop_index(op.f('ix_content_notas_clinicas_images_id'), table_name='content_notas_clinicas_images')
    op.drop_table('content_notas_clinicas_images')
    
    op.drop_index(op.f('ix_content_videoclases_id'), table_name='content_videoclases')
    op.drop_table('content_videoclases')
    
    op.drop_index(op.f('ix_content_preguntas_id'), table_name='content_preguntas')
    op.drop_table('content_preguntas')
    
    op.drop_index(op.f('ix_content_cuestionarios_id'), table_name='content_cuestionarios')
    op.drop_table('content_cuestionarios')
    
    op.drop_index(op.f('ix_content_notas_clinicas_id'), table_name='content_notas_clinicas')
    op.drop_table('content_notas_clinicas')
    
    op.drop_index(op.f('ix_content_opciones_caso_id'), table_name='content_opciones_caso')
    op.drop_table('content_opciones_caso')
    
    op.drop_index(op.f('ix_content_preguntas_caso_id'), table_name='content_preguntas_caso')
    op.drop_table('content_preguntas_caso')
    
    op.drop_index(op.f('ix_content_casos_clinicos_id'), table_name='content_casos_clinicos')
    op.drop_table('content_casos_clinicos')
    
    # Finally drop the parent table
    op.drop_index(op.f('ix_content_lecciones_id'), table_name='content_lecciones')
    op.drop_table('content_lecciones')


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('content_preguntas',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('cuestionario_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('text', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('answers', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('explanation', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['cuestionario_id'], ['content_cuestionarios.id'], name=op.f('content_preguntas_cuestionario_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('content_preguntas_pkey'))
    )
    op.create_index(op.f('ix_content_preguntas_id'), 'content_preguntas', ['id'], unique=False)
    op.create_table('content_notas_clinicas',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('content_notas_clinicas_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('leccion_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['leccion_id'], ['content_lecciones.id'], name='content_notas_clinicas_leccion_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='content_notas_clinicas_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_content_notas_clinicas_id'), 'content_notas_clinicas', ['id'], unique=False)
    op.create_table('content_cuestionarios',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('leccion_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['leccion_id'], ['content_lecciones.id'], name=op.f('content_cuestionarios_leccion_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('content_cuestionarios_pkey'))
    )
    op.create_index(op.f('ix_content_cuestionarios_id'), 'content_cuestionarios', ['id'], unique=False)
    op.create_table('content_opciones_caso',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('pregunta_caso_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('texto', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('es_correcta', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('explicacion', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('imagen_url', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('imagen_path', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['pregunta_caso_id'], ['content_preguntas_caso.id'], name=op.f('content_opciones_caso_pregunta_caso_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('content_opciones_caso_pkey'))
    )
    op.create_index(op.f('ix_content_opciones_caso_id'), 'content_opciones_caso', ['id'], unique=False)
    op.create_table('content_casos_clinicos',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('content_casos_clinicos_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('especialidad', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('tema', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('descripcion', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='content_casos_clinicos_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_content_casos_clinicos_id'), 'content_casos_clinicos', ['id'], unique=False)
    op.create_table('content_preguntas_caso',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('caso_clinico_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('texto', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('imagen_url', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('imagen_path', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['caso_clinico_id'], ['content_casos_clinicos.id'], name=op.f('content_preguntas_caso_caso_clinico_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('content_preguntas_caso_pkey'))
    )
    op.create_index(op.f('ix_content_preguntas_caso_id'), 'content_preguntas_caso', ['id'], unique=False)
    op.create_table('content_lecciones',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('content_lecciones_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('especialidad', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('tema', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='content_lecciones_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_content_lecciones_id'), 'content_lecciones', ['id'], unique=False)
    op.create_table('content_videoclases',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('leccion_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('url', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('file_path', sa.VARCHAR(length=512), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['leccion_id'], ['content_lecciones.id'], name=op.f('content_videoclases_leccion_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('content_videoclases_pkey'))
    )
    op.create_index(op.f('ix_content_videoclases_id'), 'content_videoclases', ['id'], unique=False)
    op.create_table('content_notas_clinicas_images',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('nota_clinica_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('image_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('url', sa.VARCHAR(length=512), autoincrement=False, nullable=False),
    sa.Column('file_path', sa.VARCHAR(length=512), autoincrement=False, nullable=False),
    sa.Column('caption', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['nota_clinica_id'], ['content_notas_clinicas.id'], name=op.f('content_notas_clinicas_images_nota_clinica_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('content_notas_clinicas_images_pkey'))
    )
    op.create_index(op.f('ix_content_notas_clinicas_images_id'), 'content_notas_clinicas_images', ['id'], unique=False)
    op.drop_index(op.f('ix_videos_cortos_data_id'), table_name='videos_cortos_data')
    op.drop_table('videos_cortos_data')
    op.drop_index(op.f('ix_videoclases_data_id'), table_name='videoclases_data')
    op.drop_table('videoclases_data')
    op.drop_index(op.f('ix_notas_clinicas_data_id'), table_name='notas_clinicas_data')
    op.drop_table('notas_clinicas_data')
    op.drop_index(op.f('ix_flashcards_data_id'), table_name='flashcards_data')
    op.drop_table('flashcards_data')
    op.drop_index(op.f('ix_cuestionarios_data_id'), table_name='cuestionarios_data')
    op.drop_table('cuestionarios_data')
    op.drop_index(op.f('ix_casos_clinicos_data_id'), table_name='casos_clinicos_data')
    op.drop_table('casos_clinicos_data')
    # ### end Alembic commands ###
