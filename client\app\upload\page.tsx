"use client";

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Upload, Video, Image, HelpCircle, Trash2, Edit3, Plus, BookOpen, FileQuestion, Brain } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EspecialidadTemaSelector } from "@/components/ui/especialidad-tema-selector";
import NotasClinicasComponent from "./notas-clinicas";
import CasosClinicosComponent from "./casos-clinicos";
import CuestionarioComponent from "./cuestionario";
import FlashcardsComponent from "./flashcards";
import VideoClasesComponent from "./video-clases";
import VideoCortos from "./video-cortos";

interface EspecialidadTemaData {
  especialidad: string
  sistema: string
  tema: string
  titulo: string
}

export default function UploadPage() {
  const [sharedData, setSharedData] = useState<EspecialidadTemaData>({
    especialidad: '',
    sistema: '',
    tema: '',
    titulo: ''
  });

  const handleSharedDataChange = (data: EspecialidadTemaData) => {
    setSharedData(data);
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Contenido Educativo</h1>
          <p className="text-gray-600">Gestiona y sube contenido educativo médico</p>
          
          {/* Medical Topics Summary */}
          <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">📚 Temario Médico Completo</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">5</div>
                <div className="text-blue-700">Especialidades</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">65</div>
                <div className="text-green-700">Sistemas Médicos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">410+</div>
                <div className="text-purple-700">Temas Específicos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">100%</div>
                <div className="text-orange-700">Cobertura Residencia</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">MX</div>
                <div className="text-red-700">Programas Nacionales</div>
              </div>
            </div>
            <p className="text-xs text-gray-600 mt-3 text-center">
              Incluye: Medicina Interna • Cirugía General • Ginecología y Obstetricia • Pediatría • Programas Nacionales de Salud
            </p>
          </div>
        </div>

        {/* Shared Especialidad-Tema Selector */}
        <EspecialidadTemaSelector
          value={sharedData}
          onChange={handleSharedDataChange}
        />

        <Tabs defaultValue="videoclases" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="videoclases" className="flex items-center gap-2">
              <Video className="h-4 w-4" />
              Videoclases
            </TabsTrigger>
            <TabsTrigger value="video-cortos" className="flex items-center gap-2">
              <Video className="h-4 w-4" />
              Video Cortos
            </TabsTrigger>
            <TabsTrigger value="notas-clinicas" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Notas Clínicas
            </TabsTrigger>
            <TabsTrigger value="casos-clinicos" className="flex items-center gap-2">
              <FileQuestion className="h-4 w-4" />
              Casos Clínicos
            </TabsTrigger>
            <TabsTrigger value="cuestionarios" className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4" />
              Cuestionarios
            </TabsTrigger>
            <TabsTrigger value="flashcards" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Flashcards
            </TabsTrigger>
          </TabsList>

          <TabsContent value="videoclases">
            <VideoClasesComponent sharedData={sharedData} />
          </TabsContent>

          <TabsContent value="video-cortos">
            <VideoCortos sharedData={sharedData} />
          </TabsContent>

          <TabsContent value="notas-clinicas">
            <NotasClinicasComponent sharedData={sharedData} />
          </TabsContent>

          <TabsContent value="casos-clinicos">
            <CasosClinicosComponent sharedData={sharedData} />
          </TabsContent>

          <TabsContent value="cuestionarios">
            <CuestionarioComponent sharedData={sharedData} />
          </TabsContent>

          <TabsContent value="flashcards">
            <FlashcardsComponent sharedData={sharedData} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
