"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Upload,
  Image as ImageIcon,
  HelpCircle,
  CheckCircle,
  AlertCircle,
  X,
  Plus,
  Trash2,
  Eye,
  Edit3,
  Save,
  ArrowLeft,
  ArrowRight,
  ZoomIn,
  Calendar,
  FileText,
  Info
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface EspecialidadTemaData {
  especialidad: string
  sistema: string
  tema: string
  titulo: string
}

interface ImageData {
  id: string;
  url: string;
  filename?: string;
  file_path?: string;
}

interface OpcionData {
  id: string;
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
  imagen_url?: string;
  imagen_path?: string;
}

interface PreguntaData {
  id: string;
  caso_clinico: string;
  pregunta: string;
  imagen_url?: string;
  imagen_path?: string;
  opciones: OpcionData[];
  respuesta_correcta: string;
  explicacion_general: string;
}

interface CasosClinicosComponentProps {
  sharedData: EspecialidadTemaData
}

interface UploadedImage {
  id: string;
  file: File;
  url: string;
}

interface OpcionRespuesta {
  id: string;
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
  imagen?: UploadedImage;
}

interface Pregunta {
  id: string;
  caso_clinico: string;
  pregunta: string;
  respuesta_correcta: string;
  explicacion_general: string;
  imagen?: UploadedImage;
  opciones: OpcionRespuesta[];
}

interface CasoClinico {
  id: number;
  title: string;
  especialidad: string;
  tema: string;
  descripcion: string;
  images?: ImageData[];
  preguntas: PreguntaData[];
  created_at: string;
}

// Helper function to get API base URL
function getApiBaseUrl(): string {
  const base = process.env.NEXT_PUBLIC_API_BASE_URL?.replace(/\/$/, '');
  return base || 'http://localhost:8000';
}

export default function CasosClinicosComponent({ sharedData }: CasosClinicosComponentProps) {
  const [descripcion, setDescripcion] = useState('');
  const [caseImages, setCaseImages] = useState<UploadedImage[]>([]);
  const [preguntas, setPreguntas] = useState<Pregunta[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [casos, setCasos] = useState<CasoClinico[]>([]);
  const [jsonContent, setJsonContent] = useState('');
  const [selectedCaso, setSelectedCaso] = useState<CasoClinico | null>(null);
  const [isViewingCase, setIsViewingCase] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedCaso, setEditedCaso] = useState<CasoClinico | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [newCaseImages, setNewCaseImages] = useState<UploadedImage[]>([]);
  const [newQuestionImages, setNewQuestionImages] = useState<{[key: number]: UploadedImage}>({});
  const [newOptionImages, setNewOptionImages] = useState<{[key: string]: UploadedImage}>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [caseToDelete, setCaseToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
    show: boolean;
  }>({ type: 'success', message: '', show: false });

  // Show notification
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message, show: true });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  // Fetch clinical cases
  const fetchCasos = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/clinical-cases`);
      if (!response.ok) throw new Error('Error al cargar los casos clínicos');
      const data = await response.json();
      setCasos(data);
    } catch (err) {
      console.error('Error fetching clinical cases:', err);
      setError('Error al cargar los casos clínicos');
    }
  };

  // Load cases on mount
  useEffect(() => {
    fetchCasos();
  }, []);

  // Handle case image upload
  const handleCaseImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const id = Math.random().toString(36).substring(2, 11);
        const url = URL.createObjectURL(file);
        setCaseImages(prev => [...prev, { id, file, url }]);
      }
    });
  };

  // Handle question/option image upload
  const handleQuestionImageUpload = (questionId: string, file: File, type: 'question' | 'option', optionId?: string) => {
    const id = Math.random().toString(36).substring(2, 11);
    const url = URL.createObjectURL(file);
    const newImage: UploadedImage = { id, file, url };

    setPreguntas(prev => prev.map(pregunta => {
      if (pregunta.id === questionId) {
        if (type === 'question') {
          return { ...pregunta, imagen: newImage };
        } else if (type === 'option' && optionId) {
          return {
            ...pregunta,
            opciones: pregunta.opciones.map(opcion =>
              opcion.id === optionId ? { ...opcion, imagen: newImage } : opcion
            )
          };
        }
      }
      return pregunta;
    }));
  };

  // Remove case image
  const removeCaseImage = (id: string) => {
    setCaseImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.url);
      }
      return prev.filter(img => img.id !== id);
    });
  };

  // Add new question
  const addPregunta = () => {
    const newPregunta: Pregunta = {
      id: Math.random().toString(36).substring(2, 11),
      caso_clinico: '',
      pregunta: '',
      respuesta_correcta: 'A',
      explicacion_general: '',
      opciones: [
        { id: Math.random().toString(36).substring(2, 11), opcion: '', es_correcta: true, explicacion: '' },
        { id: Math.random().toString(36).substring(2, 11), opcion: '', es_correcta: false, explicacion: '' },
        { id: Math.random().toString(36).substring(2, 11), opcion: '', es_correcta: false, explicacion: '' },
        { id: Math.random().toString(36).substring(2, 11), opcion: '', es_correcta: false, explicacion: '' },
      ]
    };
    setPreguntas(prev => [...prev, newPregunta]);
  };

  // Remove question
  const removePregunta = (questionId: string) => {
    setPreguntas(prev => prev.filter(p => p.id !== questionId));
  };

  // Update question
  const updatePregunta = (questionId: string, field: string, value: string | undefined | UploadedImage) => {
    setPreguntas(prev => prev.map(pregunta =>
      pregunta.id === questionId ? { ...pregunta, [field]: value } : pregunta
    ));
  };

  // Update option
  const updateOpcion = (questionId: string, optionId: string, field: string, value: string | boolean | undefined | UploadedImage) => {
    setPreguntas(prev => prev.map(pregunta => {
      if (pregunta.id === questionId) {
        return {
          ...pregunta,
          opciones: pregunta.opciones.map(opcion => {
            if (opcion.id === optionId) {
              // If marking as correct, unmark others
              if (field === 'es_correcta' && value === true) {
                pregunta.opciones.forEach(op => op.es_correcta = false);
              }
              return { ...opcion, [field]: value };
            }
            return opcion;
          })
        };
      }
      return pregunta;
    }));
  };

  // Reset form
  const resetForm = () => {
    setDescripcion('');
    setCaseImages([]);
    setPreguntas([]);
    setError(null);
    setSuccess(false);
  };

  // Handle case selection for detailed view
  const handleViewCase = (caso: CasoClinico) => {
    setSelectedCaso(caso);
    setEditedCaso(JSON.parse(JSON.stringify(caso))); // Deep copy
    setCurrentQuestionIndex(0);
    setIsViewingCase(true);
    setIsEditing(false);
  };

  // Close detailed view
  const handleCloseView = () => {
    setSelectedCaso(null);
    setEditedCaso(null);
    setIsViewingCase(false);
    setCurrentQuestionIndex(0);
    setSelectedImageUrl(null);
    setIsEditing(false);
    // Clean up new images
    setNewCaseImages([]);
    setNewQuestionImages({});
    setNewOptionImages({});
  };

  // Toggle edit mode
  const handleToggleEdit = () => {
    if (isEditing) {
      // Cancel editing - restore original data
      setEditedCaso(selectedCaso ? JSON.parse(JSON.stringify(selectedCaso)) : null);
    }
    setIsEditing(!isEditing);
  };

  // Save changes
  const handleSaveChanges = async () => {
    if (!editedCaso) return;

    setIsSaving(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('title', editedCaso.title);
      formData.append('descripcion', editedCaso.descripcion);
      formData.append('preguntas', JSON.stringify(editedCaso.preguntas));

      // Add new case images
      newCaseImages.forEach((image) => {
        formData.append('new_case_images', image.file);
        formData.append('new_case_image_ids', image.id);
      });

      // Add new question images
      Object.entries(newQuestionImages).forEach(([questionIndex, image]) => {
        formData.append('new_question_images', image.file);
        formData.append('new_question_image_ids', image.id);
        formData.append('new_question_image_indices', questionIndex);
      });

      // Add new option images
      Object.entries(newOptionImages).forEach(([key, image]) => {
        formData.append('new_option_images', image.file);
        formData.append('new_option_image_ids', image.id);
        formData.append('new_option_image_keys', key);
      });

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/clinical-cases/${editedCaso.id}`, {
        method: 'PUT',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al actualizar el caso clínico');
      }

      await response.json();

      // Update local state
      setSelectedCaso(editedCaso);
      setCasos(prev => prev.map(caso =>
        caso.id === editedCaso.id ? editedCaso : caso
      ));

      // Clean up new images
      setNewCaseImages([]);
      setNewQuestionImages({});
      setNewOptionImages({});

      setIsEditing(false);
      showNotification('success', 'Caso clínico actualizado exitosamente');

    } catch (err) {
      console.error('Error saving changes:', err);
      showNotification('error', err instanceof Error ? err.message : 'Error al guardar los cambios');
    } finally {
      setIsSaving(false);
    }
  };

  // Update edited case field
  const updateEditedCaseField = (field: string, value: string | ImageData[] | PreguntaData[]) => {
    if (!editedCaso) return;
    setEditedCaso(prev => prev ? { ...prev, [field]: value } : null);
  };

  // Update edited question
  const updateEditedQuestion = (questionIndex: number, field: string, value: string | null) => {
    if (!editedCaso) return;
    setEditedCaso(prev => {
      if (!prev) return null;
      const newPreguntas = [...prev.preguntas];
      newPreguntas[questionIndex] = { ...newPreguntas[questionIndex], [field]: value };
      return { ...prev, preguntas: newPreguntas };
    });
  };

  // Update edited option
  const updateEditedOption = (questionIndex: number, optionIndex: number, field: string, value: string | boolean | null) => {
    if (!editedCaso) return;
    setEditedCaso(prev => {
      if (!prev) return null;
      const newPreguntas = [...prev.preguntas];
      const newOpciones = [...newPreguntas[questionIndex].opciones];

      // If marking as correct, unmark others
      if (field === 'es_correcta' && value === true) {
        newOpciones.forEach(op => op.es_correcta = false);
      }

      newOpciones[optionIndex] = { ...newOpciones[optionIndex], [field]: value };
      newPreguntas[questionIndex] = { ...newPreguntas[questionIndex], opciones: newOpciones };
      return { ...prev, preguntas: newPreguntas };
    });
  };

  // Show delete confirmation
  const showDeleteConfirmation = (casoId: number) => {
    setCaseToDelete(casoId);
    setShowDeleteConfirm(true);
  };

  // Cancel delete
  const cancelDelete = () => {
    setCaseToDelete(null);
    setShowDeleteConfirm(false);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!caseToDelete) return;

    setIsDeleting(true);
    setError(null);

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/clinical-cases/${caseToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al eliminar el caso clínico');
      }

      // Remove from local state
      setCasos(prev => prev.filter(caso => caso.id !== caseToDelete));

      // If we're viewing the deleted case, close the view
      if (selectedCaso && selectedCaso.id === caseToDelete) {
        handleCloseView();
      }

      showNotification('success', 'Caso clínico eliminado exitosamente');

      // Close confirmation modal
      setShowDeleteConfirm(false);
      setCaseToDelete(null);

    } catch (err) {
      console.error('Error deleting case:', err);
      showNotification('error', err instanceof Error ? err.message : 'Error al eliminar el caso clínico');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle case image upload in edit mode
  const handleEditCaseImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const id = Math.random().toString(36).substring(2, 11);
        const url = URL.createObjectURL(file);
        setNewCaseImages(prev => [...prev, { id, file, url }]);
      }
    });
  };

  // Remove case image in edit mode
  const removeEditCaseImage = (imageId: string, isNew: boolean = false) => {
    if (isNew) {
      setNewCaseImages(prev => {
        const imageToRemove = prev.find(img => img.id === imageId);
        if (imageToRemove) {
          URL.revokeObjectURL(imageToRemove.url);
        }
        return prev.filter(img => img.id !== imageId);
      });
    } else {
      // Remove from existing images
      if (editedCaso) {
        const updatedImages = editedCaso.images?.filter((img: ImageData) => img.id !== imageId) || [];
        updateEditedCaseField('images', updatedImages);
      }
    }
  };

  // Handle question image upload in edit mode
  const handleEditQuestionImageUpload = (questionIndex: number, file: File) => {
    const id = Math.random().toString(36).substring(2, 11);
    const url = URL.createObjectURL(file);
    const newImage: UploadedImage = { id, file, url };

    setNewQuestionImages(prev => ({ ...prev, [questionIndex]: newImage }));
  };

  // Remove question image in edit mode
  const removeEditQuestionImage = (questionIndex: number) => {
    // Remove from new images if exists
    setNewQuestionImages(prev => {
      const imageToRemove = prev[questionIndex];
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.url);
      }
      const newImages = { ...prev };
      delete newImages[questionIndex];
      return newImages;
    });

    // Remove from edited case
    updateEditedQuestion(questionIndex, 'imagen_url', null);
  };

  // Handle option image upload in edit mode
  const handleEditOptionImageUpload = (questionIndex: number, optionIndex: number, file: File) => {
    const id = Math.random().toString(36).substring(2, 11);
    const url = URL.createObjectURL(file);
    const newImage: UploadedImage = { id, file, url };
    const key = `${questionIndex}-${optionIndex}`;

    setNewOptionImages(prev => ({ ...prev, [key]: newImage }));
  };

  // Remove option image in edit mode
  const removeEditOptionImage = (questionIndex: number, optionIndex: number) => {
    const key = `${questionIndex}-${optionIndex}`;

    // Remove from new images if exists
    setNewOptionImages(prev => {
      const imageToRemove = prev[key];
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.url);
      }
      const newImages = { ...prev };
      delete newImages[key];
      return newImages;
    });

    // Remove from edited case
    updateEditedOption(questionIndex, optionIndex, 'imagen_url', null);
  };

  // Filter cases based on search term
  const filteredCasos = casos.filter(caso =>
    caso.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    caso.descripcion.toLowerCase().includes(searchTerm.toLowerCase()) ||
    caso.especialidad.toLowerCase().includes(searchTerm.toLowerCase()) ||
    caso.tema.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Navigate between questions
  const handlePreviousQuestion = () => {
    if (selectedCaso && currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleNextQuestion = () => {
    if (selectedCaso && currentQuestionIndex < selectedCaso.preguntas.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // Handle JSON content submission
  const handleJsonSubmit = async () => {
    if (!jsonContent.trim()) {
      setError('Por favor ingrese el contenido JSON');
      return;
    }

    if (!sharedData.especialidad || !sharedData.tema) {
      setError('Por favor complete la información general (especialidad y tema) antes de procesar el JSON');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const formData = new FormData();
      formData.append('json_content', jsonContent);
      formData.append('especialidad', sharedData.especialidad);
      formData.append('sistema', sharedData.sistema);
      formData.append('tema', sharedData.tema);
      if (sharedData.titulo) {
        formData.append('titulo', sharedData.titulo);
      }

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/clinical-cases/json`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar el contenido JSON');
      }

      const result = await response.json();
      
      if (result.success) {
        setSuccess(true);
        setError(null);
        setJsonContent('');
        fetchCasos();
      } else {
        throw new Error(result.message || 'Error al procesar el contenido JSON');
      }
    } catch (err) {
      console.error('Error processing JSON:', err);
      setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!sharedData.especialidad || !sharedData.tema || !sharedData.titulo) {
      setError('Por favor complete la información general (especialidad, tema y título)');
      return;
    }

    if (!descripcion.trim()) {
      setError('Por favor ingrese la descripción del caso clínico');
      return;
    }

    if (preguntas.length === 0) {
      setError('Por favor agregue al menos una pregunta');
      return;
    }

    // Validate questions
    for (let i = 0; i < preguntas.length; i++) {
      const pregunta = preguntas[i];
      if (!pregunta.pregunta.trim()) {
        setError(`La pregunta ${i + 1} debe tener texto`);
        return;
      }

      const hasCorrectAnswer = pregunta.opciones.some(op => op.es_correcta);
      if (!hasCorrectAnswer) {
        setError(`La pregunta ${i + 1} debe tener al menos una respuesta correcta`);
        return;
      }

      for (let j = 0; j < pregunta.opciones.length; j++) {
        const opcion = pregunta.opciones[j];
        if (!opcion.opcion.trim()) {
          setError(`La opción ${j + 1} de la pregunta ${i + 1} debe tener texto`);
          return;
        }
        if (opcion.es_correcta && !opcion.explicacion.trim()) {
          setError(`La opción correcta ${j + 1} de la pregunta ${i + 1} debe tener explicación`);
          return;
        }
      }
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      const formData = new FormData();
      formData.append('especialidad', sharedData.especialidad);
      formData.append('tema', sharedData.tema);
      formData.append('titulo', sharedData.titulo);
      formData.append('descripcion', descripcion);

      // Add case description images
      caseImages.forEach((image) => {
        formData.append('case_images', image.file);
        formData.append('case_image_ids', image.id);
      });

      // Add questions data
      const questionsData = preguntas.map(pregunta => ({
        caso_clinico: pregunta.caso_clinico,
        pregunta: pregunta.pregunta,
        respuesta_correcta: pregunta.respuesta_correcta,
        explicacion_general: pregunta.explicacion_general,
        imagen_id: pregunta.imagen?.id || null,
        opciones: pregunta.opciones.map(opcion => ({
          opcion: opcion.opcion,
          es_correcta: opcion.es_correcta,
          explicacion: opcion.explicacion,
          imagen_id: opcion.imagen?.id || null
        }))
      }));

      formData.append('preguntas', JSON.stringify(questionsData));

      // Add question images
      preguntas.forEach(pregunta => {
        if (pregunta.imagen) {
          formData.append('question_images', pregunta.imagen.file);
          formData.append('question_image_ids', pregunta.imagen.id);
        }
        
        pregunta.opciones.forEach(opcion => {
          if (opcion.imagen) {
            formData.append('option_images', opcion.imagen.file);
            formData.append('option_image_ids', opcion.imagen.id);
          }
        });
      });

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/clinical-cases`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al subir el caso clínico');
      }

      const result = await response.json();
      
      if (result.success) {
        setSuccess(true);
        resetForm();
        fetchCasos();
      } else {
        throw new Error(result.message || 'Error al subir el caso clínico');
      }
    } catch (err) {
      console.error('Error:', err);
      setError(err instanceof Error ? err.message : 'Error al subir el caso clínico');
    } finally {
      setIsUploading(false);
    }
  };

  // Render detailed case view
  const renderDetailedCaseView = () => {
    if (!selectedCaso || !editedCaso) return null;

    const displayCaso = isEditing ? editedCaso : selectedCaso;
    const currentQuestion = displayCaso.preguntas[currentQuestionIndex];


    return (
      <div className="fixed inset-0 bg-white z-50 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="border-b bg-white px-6 py-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCloseView}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Volver
              </Button>
              <div>
                {isEditing ? (
                  <Input
                    value={editedCaso?.title || ''}
                    onChange={(e) => updateEditedCaseField('title', e.target.value)}
                    className="text-xl font-semibold border-0 px-0 focus-visible:ring-0"
                    placeholder="Título del caso clínico..."
                  />
                ) : (
                  <h1 className="text-xl font-semibold">{displayCaso.title}</h1>
                )}
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Badge variant="outline">{displayCaso.especialidad}</Badge>
                  <span>•</span>
                  <span>{displayCaso.tema}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(displayCaso.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleToggleEdit}
                    disabled={isSaving}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancelar
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSaveChanges}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <div className="flex items-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                        Guardando...
                      </div>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Guardar
                      </>
                    )}
                  </Button>
                </>
              ) : (
                <Button variant="outline" size="sm" onClick={handleToggleEdit}>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Editar
                </Button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="max-w-4xl mx-auto p-6 space-y-8">
                {/* Alerts */}
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert className="border-green-200 bg-green-50">
                    <AlertDescription className="text-green-800">
                      ¡Cambios guardados exitosamente!
                    </AlertDescription>
                  </Alert>
                )}
                {/* Case Description */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Descripción del Caso
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {isEditing ? (
                      <Textarea
                        value={editedCaso?.descripcion || ''}
                        onChange={(e) => updateEditedCaseField('descripcion', e.target.value)}
                        rows={8}
                        className="resize-none"
                        placeholder="Descripción del caso clínico..."
                      />
                    ) : (
                      <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                        {displayCaso.descripcion}
                      </p>
                    )}

                    {/* Case Images */}
                    {((displayCaso.images && displayCaso.images.length > 0) || newCaseImages.length > 0 || isEditing) && (
                      <div className="mt-6">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="text-sm font-medium">Imágenes del caso:</h4>
                          {isEditing && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => document.getElementById('edit-case-images')?.click()}
                              className="flex items-center gap-2"
                            >
                              <Plus className="h-4 w-4" />
                              Agregar Imagen
                            </Button>
                          )}
                        </div>

                        <input
                          id="edit-case-images"
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleEditCaseImageUpload}
                          className="hidden"
                        />

                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {/* Existing images */}
                          {displayCaso.images?.map((image: ImageData, index: number) => (
                            <div key={`existing-${index}`} className="relative group cursor-pointer">
                              <img
                                src={image.url}
                                alt={`Caso clínico ${index + 1}`}
                                className="w-full h-32 object-cover rounded-lg border hover:shadow-md transition-shadow"
                                onClick={() => setSelectedImageUrl(image.url)}
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
                                <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                              </div>
                              {isEditing && (
                                <Button
                                  type="button"
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => removeEditCaseImage(image.id, false)}
                                  className="absolute -top-2 -right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          ))}

                          {/* New images */}
                          {newCaseImages.map((image) => (
                            <div key={`new-${image.id}`} className="relative group cursor-pointer">
                              <img
                                src={image.url}
                                alt="Nueva imagen"
                                className="w-full h-32 object-cover rounded-lg border hover:shadow-md transition-shadow"
                                onClick={() => setSelectedImageUrl(image.url)}
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center">
                                <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                              </div>
                              <Button
                                type="button"
                                size="sm"
                                variant="destructive"
                                onClick={() => removeEditCaseImage(image.id, true)}
                                className="absolute -top-2 -right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                              <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                Nueva
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Questions Section */}
                {displayCaso.preguntas && displayCaso.preguntas.length > 0 && (
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <HelpCircle className="h-5 w-5" />
                          Preguntas ({displayCaso.preguntas.length})
                        </CardTitle>
                        {displayCaso.preguntas.length > 1 && (
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handlePreviousQuestion}
                              disabled={currentQuestionIndex === 0}
                            >
                              <ArrowLeft className="h-4 w-4" />
                            </Button>
                            <span className="text-sm text-gray-500">
                              {currentQuestionIndex + 1} de {displayCaso.preguntas.length}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleNextQuestion}
                              disabled={currentQuestionIndex === displayCaso.preguntas.length - 1}
                            >
                              <ArrowRight className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      {currentQuestion && (
                        <div className="space-y-6">
                          {/* Case Clinical Description */}
                          <div>
                            <h4 className="text-sm font-medium text-gray-600 mb-2">Caso Clínico:</h4>
                            {isEditing ? (
                              <Textarea
                                value={editedCaso?.preguntas[currentQuestionIndex]?.caso_clinico || ''}
                                onChange={(e) => updateEditedQuestion(currentQuestionIndex, 'caso_clinico', e.target.value)}
                                rows={4}
                                className="resize-none mb-4"
                                placeholder="Descripción del caso clínico..."
                              />
                            ) : (
                              <p className="text-gray-700 leading-relaxed mb-4 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                                {currentQuestion.caso_clinico}
                              </p>
                            )}
                          </div>

                          {/* Question Text */}
                          <div>
                            <h4 className="text-sm font-medium text-gray-600 mb-2">Pregunta:</h4>
                            {isEditing ? (
                              <Textarea
                                value={editedCaso?.preguntas[currentQuestionIndex]?.pregunta || ''}
                                onChange={(e) => updateEditedQuestion(currentQuestionIndex, 'pregunta', e.target.value)}
                                rows={3}
                                className="resize-none text-lg font-medium mb-3"
                                placeholder="Texto de la pregunta..."
                              />
                            ) : (
                              <h3 className="text-lg font-medium mb-3">
                                {currentQuestion.pregunta}
                              </h3>
                            )}

                            {/* Question Image */}
                            <div className="mb-4">
                              {isEditing && (
                                <div className="mb-3">
                                  <Label className="text-sm">Imagen de la pregunta (opcional)</Label>
                                  <div className="flex gap-2 mt-1">
                                    <input
                                      type="file"
                                      accept="image/*"
                                      onChange={(e) => {
                                        const file = e.target.files?.[0];
                                        if (file) {
                                          handleEditQuestionImageUpload(currentQuestionIndex, file);
                                        }
                                      }}
                                      className="text-sm"
                                    />
                                  </div>
                                </div>
                              )}

                              {/* Show existing or new question image */}
                              {(newQuestionImages[currentQuestionIndex] || currentQuestion.imagen_url) && (
                                <div className="relative inline-block">
                                  <img
                                    src={newQuestionImages[currentQuestionIndex]?.url || currentQuestion.imagen_url}
                                    alt="Pregunta"
                                    className="max-w-md h-auto rounded-lg border cursor-pointer hover:shadow-md transition-shadow"
                                    onClick={() => {
                                      const imageUrl = newQuestionImages[currentQuestionIndex]?.url || currentQuestion.imagen_url;
                                      if (imageUrl) setSelectedImageUrl(imageUrl);
                                    }}
                                  />
                                  {isEditing && (
                                    <Button
                                      type="button"
                                      size="sm"
                                      variant="destructive"
                                      onClick={() => removeEditQuestionImage(currentQuestionIndex)}
                                      className="absolute -top-2 -right-2 h-6 w-6 p-0"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  )}
                                  {newQuestionImages[currentQuestionIndex] && (
                                    <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                                      Nueva
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Options */}
                          <div className="space-y-3">
                            <h4 className="font-medium text-gray-900">Opciones:</h4>
                            {currentQuestion.opciones?.map((opcion: OpcionData, index: number) => (
                              <div
                                key={index}
                                className={`border rounded-lg p-4 ${
                                  opcion.es_correcta
                                    ? 'border-green-200 bg-green-50'
                                    : 'border-gray-200 bg-gray-50'
                                }`}
                              >
                                <div className="flex items-start gap-3">
                                  <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${
                                    opcion.es_correcta
                                      ? 'bg-green-100 text-green-700'
                                      : 'bg-gray-100 text-gray-600'
                                  }`}>
                                    {String.fromCharCode(65 + index)}
                                  </div>
                                  <div className="flex-1">
                                    {isEditing ? (
                                      <div className="space-y-2">
                                        <Input
                                          value={opcion.opcion}
                                          onChange={(e) => updateEditedOption(currentQuestionIndex, index, 'opcion', e.target.value)}
                                          placeholder="Texto de la opción"
                                          className="mb-2"
                                        />
                                        <div className="flex items-center gap-2">
                                          <Button
                                            type="button"
                                            variant={opcion.es_correcta ? "default" : "outline"}
                                            size="sm"
                                            onClick={() => updateEditedOption(currentQuestionIndex, index, 'es_correcta', !opcion.es_correcta)}
                                            className="flex items-center gap-1"
                                          >
                                            {opcion.es_correcta ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                                            {opcion.es_correcta ? 'Correcta' : 'Marcar correcta'}
                                          </Button>
                                        </div>
                                      </div>
                                    ) : (
                                      <p className="text-gray-900 mb-2">{opcion.opcion}</p>
                                    )}

                                    {/* Option Image */}
                                    <div className="mb-3">
                                      {isEditing && (
                                        <div className="mb-2">
                                          <Label className="text-xs">Imagen de la opción (opcional)</Label>
                                          <input
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => {
                                              const file = e.target.files?.[0];
                                              if (file) {
                                                handleEditOptionImageUpload(currentQuestionIndex, index, file);
                                              }
                                            }}
                                            className="text-xs mt-1"
                                          />
                                        </div>
                                      )}

                                      {/* Show existing or new option image */}
                                      {(newOptionImages[`${currentQuestionIndex}-${index}`] || opcion.imagen_url) && (
                                        <div className="relative inline-block">
                                          <img
                                            src={newOptionImages[`${currentQuestionIndex}-${index}`]?.url || opcion.imagen_url}
                                            alt={`Opción ${String.fromCharCode(65 + index)}`}
                                            className="max-w-xs h-auto rounded border cursor-pointer hover:shadow-md transition-shadow"
                                            onClick={() => {
                                              const imageUrl = newOptionImages[`${currentQuestionIndex}-${index}`]?.url || opcion.imagen_url;
                                              if (imageUrl) setSelectedImageUrl(imageUrl);
                                            }}
                                          />
                                          {isEditing && (
                                            <Button
                                              type="button"
                                              size="sm"
                                              variant="destructive"
                                              onClick={() => removeEditOptionImage(currentQuestionIndex, index)}
                                              className="absolute -top-1 -right-1 h-5 w-5 p-0"
                                            >
                                              <X className="h-2 w-2" />
                                            </Button>
                                          )}
                                          {newOptionImages[`${currentQuestionIndex}-${index}`] && (
                                            <div className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded">
                                              Nueva
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </div>

                                    {/* Explanation */}
                                    {isEditing ? (
                                      <div className="mt-2">
                                        <Label className="text-xs">Explicación {opcion.es_correcta ? '(requerida para respuesta correcta)' : '(opcional)'}</Label>
                                        <Textarea
                                          value={opcion.explicacion || ''}
                                          onChange={(e) => updateEditedOption(currentQuestionIndex, index, 'explicacion', e.target.value)}
                                          placeholder="Explique por qué esta opción es correcta o incorrecta"
                                          rows={2}
                                          className="resize-none mt-1"
                                        />
                                      </div>
                                    ) : (
                                      opcion.explicacion && (
                                        <div className={`mt-2 p-3 rounded ${
                                          opcion.es_correcta
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-gray-100 text-gray-700'
                                        }`}>
                                          <p className="text-sm">
                                            <strong>Explicación:</strong> {opcion.explicacion}
                                          </p>
                                        </div>
                                      )
                                    )}
                                  </div>
                                  {opcion.es_correcta && (
                                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* General Explanation */}
                          <div className="mt-6 pt-6 border-t">
                            <h4 className="text-sm font-medium text-gray-600 mb-2">Explicación General:</h4>
                            {isEditing ? (
                              <Textarea
                                value={editedCaso?.preguntas[currentQuestionIndex]?.explicacion_general || ''}
                                onChange={(e) => updateEditedQuestion(currentQuestionIndex, 'explicacion_general', e.target.value)}
                                rows={3}
                                className="resize-none"
                                placeholder="Explicación general del caso y la respuesta correcta..."
                              />
                            ) : (
                              currentQuestion.explicacion_general && (
                                <div className="p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                                  <p className="text-gray-700 leading-relaxed">
                                    {currentQuestion.explicacion_general}
                                  </p>
                                </div>
                              )
                            )}
                          </div>

                          {/* Correct Answer Indicator */}
                          {!isEditing && currentQuestion.respuesta_correcta && (
                            <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                              <p className="text-sm text-green-800">
                                <strong>Respuesta correcta:</strong> Opción {currentQuestion.respuesta_correcta}
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Image Modal */}
        {selectedImageUrl && (
          <Dialog open={!!selectedImageUrl} onOpenChange={() => setSelectedImageUrl(null)}>
            <DialogContent className="max-w-4xl max-h-[90vh] p-0">
              <DialogHeader className="p-6 pb-0">
                <DialogTitle>Vista de imagen</DialogTitle>
              </DialogHeader>
              <div className="p-6 pt-0">
                <img
                  src={selectedImageUrl}
                  alt="Vista ampliada"
                  className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
                />
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    );
  };

  // Show detailed view if a case is selected
  if (isViewingCase) {
    return renderDetailedCaseView();
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Form */}
      <div className="lg:col-span-2">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <HelpCircle className="h-5 w-5" />
              Nuevo Caso Clínico
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* JSON Upload Section */}
            <div className="mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Upload className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium text-blue-900">Crear desde JSON</h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                Pega el contenido JSON para crear múltiples casos clínicos de una vez.
              </p>
              <div className="space-y-3">
                <Textarea
                  id="json-content"
                  placeholder="Pega aquí el contenido JSON..."
                  value={jsonContent}
                  onChange={(e) => setJsonContent(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <Button
                  type="button"
                  onClick={handleJsonSubmit}
                  disabled={!jsonContent.trim() || isUploading}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="h-4 w-4" />
                  {isUploading ? 'Procesando...' : 'Crear desde JSON'}
                </Button>
              </div>
              
              {/* JSON Format Example */}
              <details className="mt-3">
                <summary className="text-sm text-blue-700 cursor-pointer hover:text-blue-800">
                  Ver formato JSON requerido
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto">
                  <pre>{`[
  {
    "id": "1",
    "caso_clinico": "Un paciente de 28 años se presenta con cefalea pulsátil unilateral...",
    "pregunta": "¿Cuál es el diagnóstico más probable?",
    "opciones": [
      {
        "opcion": "A. Cefalea tensional",
        "es_correcta": false,
        "explicacion": "Incorrecto. La cefalea tensional es bilateral y opresiva..."
      },
      {
        "opcion": "B. Cefalea en racimo",
        "es_correcta": false,
        "explicacion": "Incorrecto. La cefalea en racimo es más intensa y periorbital..."
      },
      {
        "opcion": "C. Migraña sin aura",
        "es_correcta": true,
        "explicacion": "Correcto. Los síntomas descritos son característicos de migraña..."
      },
      {
        "opcion": "D. Cefalea por estímulos fríos",
        "es_correcta": false,
        "explicacion": "Incorrecto. Este tipo de cefalea es de corta duración..."
      }
    ],
    "respuesta_correcta": "C",
    "explicacion_general": "La migraña sin aura se caracteriza por cefalea pulsátil unilateral..."
  }
]`}</pre>
                </div>
              </details>
            </div>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">O crear manualmente</span>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Case Description */}
              <div className="space-y-2">
                <Label htmlFor="descripcion" className="text-sm font-medium">Descripción del Caso</Label>
                <Textarea
                  id="descripcion"
                  placeholder="Describa el caso clínico: presentación del paciente, síntomas, antecedentes relevantes, etc."
                  value={descripcion}
                  onChange={(e) => setDescripcion(e.target.value)}
                  rows={6}
                  className="resize-none"
                  required
                />
              </div>

              {/* Case Images */}
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <ImageIcon className="h-4 w-4" />
                  Imágenes del Caso (opcional)
                </Label>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById('case-images')?.click()}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Agregar Imagen
                  </Button>
                  <input
                    id="case-images"
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleCaseImageUpload}
                    className="hidden"
                  />
                </div>
                
                {caseImages.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                    {caseImages.map((image) => (
                      <div key={image.id} className="relative group">
                        <img
                          src={image.url}
                          alt="Caso clínico"
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        <Button
                          type="button"
                          size="sm"
                          variant="destructive"
                          onClick={() => removeCaseImage(image.id)}
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Questions Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Preguntas</Label>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addPregunta}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Agregar Pregunta
                  </Button>
                </div>

                {preguntas.map((pregunta, preguntaIndex) => (
                  <Card key={pregunta.id} className="border border-gray-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">Pregunta {preguntaIndex + 1}</CardTitle>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removePregunta(pregunta.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Case Clinical Description */}
                      <div className="space-y-2">
                        <Label className="text-sm">Caso Clínico</Label>
                        <Textarea
                          placeholder="Descripción del caso clínico..."
                          value={pregunta.caso_clinico}
                          onChange={(e) => updatePregunta(pregunta.id, 'caso_clinico', e.target.value)}
                          rows={4}
                          className="resize-none"
                        />
                      </div>

                      {/* Question Text */}
                      <div className="space-y-2">
                        <Label className="text-sm">Pregunta</Label>
                        <Textarea
                          placeholder="¿Cuál es el diagnóstico más probable?"
                          value={pregunta.pregunta}
                          onChange={(e) => updatePregunta(pregunta.id, 'pregunta', e.target.value)}
                          rows={3}
                          className="resize-none"
                        />
                      </div>

                      {/* Question Image */}
                      <div className="space-y-2">
                        <Label className="text-sm">Imagen de la pregunta (opcional)</Label>
                        <div className="flex gap-2">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handleQuestionImageUpload(pregunta.id, file, 'question');
                              }
                            }}
                            className="text-sm"
                          />
                        </div>
                        {pregunta.imagen && (
                          <div className="relative inline-block">
                            <img
                              src={pregunta.imagen.url}
                              alt="Pregunta"
                              className="w-32 h-32 object-cover rounded border"
                            />
                            <Button
                              type="button"
                              size="sm"
                              variant="destructive"
                              onClick={() => updatePregunta(pregunta.id, 'imagen', undefined)}
                              className="absolute -top-2 -right-2 h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>

                      {/* Options */}
                      <div className="space-y-3">
                        <Label className="text-sm">Opciones de respuesta</Label>
                        {pregunta.opciones.map((opcion, opcionIndex) => (
                          <div key={opcion.id} className={`border rounded-lg p-4 space-y-3 ${opcion.es_correcta ? 'border-green-200 bg-green-50' : ''}`}>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">Opción {String.fromCharCode(65 + opcionIndex)}</span>
                              <Button
                                type="button"
                                variant={opcion.es_correcta ? "default" : "outline"}
                                size="sm"
                                onClick={() => updateOpcion(pregunta.id, opcion.id, 'es_correcta', !opcion.es_correcta)}
                                className="flex items-center gap-1"
                              >
                                {opcion.es_correcta ? <CheckCircle className="h-3 w-3" /> : <AlertCircle className="h-3 w-3" />}
                                {opcion.es_correcta ? 'Correcta' : 'Marcar correcta'}
                              </Button>
                            </div>
                            
                            <Input
                              placeholder="Texto de la opción"
                              value={opcion.opcion}
                              onChange={(e) => updateOpcion(pregunta.id, opcion.id, 'opcion', e.target.value)}
                            />
                            
                            <div className="space-y-2">
                              <Label className="text-xs">Explicación {opcion.es_correcta ? '(requerida para respuesta correcta)' : '(opcional)'}</Label>
                              <Textarea
                                placeholder="Explique por qué esta opción es correcta o incorrecta"
                                value={opcion.explicacion}
                                onChange={(e) => updateOpcion(pregunta.id, opcion.id, 'explicacion', e.target.value)}
                                rows={2}
                                className="resize-none"
                              />
                            </div>

                            {/* Option Image */}
                            <div className="space-y-2">
                              <Label className="text-xs">Imagen de la opción (opcional)</Label>
                              <input
                                type="file"
                                accept="image/*"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    handleQuestionImageUpload(pregunta.id, file, 'option', opcion.id);
                                  }
                                }}
                                className="text-xs"
                              />
                              {opcion.imagen && (
                                <div className="relative inline-block">
                                  <img
                                    src={opcion.imagen.url}
                                    alt="Opción"
                                    className="w-24 h-24 object-cover rounded border"
                                  />
                                  <Button
                                    type="button"
                                    size="sm"
                                    variant="destructive"
                                    onClick={() => updateOpcion(pregunta.id, opcion.id, 'imagen', undefined)}
                                    className="absolute -top-1 -right-1 h-5 w-5 p-0"
                                  >
                                    <X className="h-2 w-2" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* General Explanation */}
                      <div className="space-y-2 pt-4 border-t">
                        <Label className="text-sm">Explicación General</Label>
                        <Textarea
                          placeholder="Explicación general del caso y la respuesta correcta..."
                          value={pregunta.explicacion_general}
                          onChange={(e) => updatePregunta(pregunta.id, 'explicacion_general', e.target.value)}
                          rows={3}
                          className="resize-none"
                        />
                      </div>

                      {/* Correct Answer */}
                      <div className="space-y-2">
                        <Label className="text-sm">Respuesta Correcta</Label>
                        <select
                          value={pregunta.respuesta_correcta}
                          onChange={(e) => updatePregunta(pregunta.id, 'respuesta_correcta', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="A">A</option>
                          <option value="B">B</option>
                          <option value="C">C</option>
                          <option value="D">D</option>
                        </select>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {preguntas.length === 0 && (
                  <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                    <HelpCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm">No hay preguntas aún</p>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={addPregunta}
                      className="mt-2"
                    >
                      Agregar Primera Pregunta
                    </Button>
                  </div>
                )}
              </div>

              {/* Alerts */}
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50">
                  <AlertDescription className="text-green-800">
                    ¡Caso clínico creado exitosamente!
                  </AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <div className="flex justify-end pt-4">
                <Button 
                  type="submit" 
                  disabled={isUploading || !sharedData.especialidad || !sharedData.tema || !sharedData.titulo}
                  className="min-w-[120px]"
                >
                  {isUploading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Creando...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Crear Caso Clínico
                    </div>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Cases List */}
      <div className="lg:col-span-1">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg">Casos Clínicos ({casos.length})</CardTitle>
            {casos.length > 0 && (
              <div className="mt-3">
                <Input
                  placeholder="Buscar casos clínicos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="text-sm"
                />
              </div>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {casos.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <HelpCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">No hay casos clínicos aún</p>
                </div>
              ) : filteredCasos.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <HelpCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">No se encontraron casos clínicos</p>
                  <p className="text-xs text-gray-400 mt-1">Intenta con otros términos de búsqueda</p>
                </div>
              ) : (
                filteredCasos.map((caso) => (
                  <div key={caso.id} className="group border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="space-y-3">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-sm truncate" title={caso.title}>
                            {caso.title}
                          </h3>
                          <div className="flex items-center gap-1 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {caso.especialidad}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleViewCase(caso)}
                            title="Ver caso completo"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => showDeleteConfirmation(caso.id)}
                            title="Eliminar caso"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Content Preview */}
                      <div className="space-y-2">
                        <p
                          className="text-xs text-gray-600 overflow-hidden"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            lineHeight: '1.3em',
                            maxHeight: '2.6em'
                          }}
                          title={caso.descripcion}
                        >
                          {caso.descripcion}
                        </p>

                        {/* Stats */}
                        <div className="flex items-center gap-3 text-xs text-gray-500">
                          {caso.preguntas && caso.preguntas.length > 0 && (
                            <div className="flex items-center gap-1">
                              <HelpCircle className="h-3 w-3" />
                              <span>{caso.preguntas.length} pregunta{caso.preguntas.length !== 1 ? 's' : ''}</span>
                            </div>
                          )}

                          {caso.images && caso.images.length > 0 && (
                            <div className="flex items-center gap-1">
                              <ImageIcon className="h-3 w-3" />
                              <span>{caso.images.length} imagen{caso.images.length !== 1 ? 'es' : ''}</span>
                            </div>
                          )}

                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(caso.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>

                        {/* Topic */}
                        <div className="flex items-center gap-1">
                          <FileText className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500 truncate" title={caso.tema}>
                            {caso.tema}
                          </span>
                        </div>
                      </div>

                      {/* Image Preview */}
                      {caso.images && caso.images.length > 0 && (
                        <div className="flex gap-1 overflow-x-auto">
                          {caso.images.slice(0, 3).map((image: ImageData, index: number) => (
                            <div key={index} className="flex-shrink-0">
                              <img
                                src={image.url}
                                alt={`Preview ${index + 1}`}
                                className="w-12 h-12 object-cover rounded border"
                              />
                            </div>
                          ))}
                          {caso.images.length > 3 && (
                            <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded border flex items-center justify-center">
                              <span className="text-xs text-gray-500">+{caso.images.length - 3}</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Confirmar eliminación
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              ¿Estás seguro de que quieres eliminar este caso clínico? Esta acción no se puede deshacer.
            </p>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={cancelDelete}
                disabled={isDeleting}
              >
                Cancelar
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Eliminando...
                  </div>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Eliminar
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Notification Toast */}
      {notification.show && (
        <div className={`fixed top-4 right-4 z-50 max-w-sm w-full transform transition-all duration-300 ${
          notification.show ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
        }`}>
          <div className={`rounded-lg shadow-lg p-4 ${
            notification.type === 'success' ? 'bg-green-50 border border-green-200' :
            notification.type === 'error' ? 'bg-red-50 border border-red-200' :
            'bg-blue-50 border border-blue-200'
          }`}>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                {notification.type === 'success' && (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                )}
                {notification.type === 'error' && (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                {notification.type === 'info' && (
                  <Info className="h-5 w-5 text-blue-600" />
                )}
              </div>
              <div className="flex-1">
                <p className={`text-sm font-medium ${
                  notification.type === 'success' ? 'text-green-800' :
                  notification.type === 'error' ? 'text-red-800' :
                  'text-blue-800'
                }`}>
                  {notification.message}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-transparent"
                onClick={() => setNotification(prev => ({ ...prev, show: false }))}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}