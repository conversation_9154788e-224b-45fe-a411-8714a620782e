from __future__ import annotations

from sqlalchemy.orm import Session
import json
from typing import List

from . import models, schemas


# Videoclases Data CRUD operations
def create_videoclase(db: Session, payload: schemas.VideoclaseCreate) -> models.VideoclasesData:
    entity = models.VideoclasesData(**payload.model_dump())
    db.add(entity)
    db.commit()
    db.refresh(entity)
    return entity


def get_videoclase(db: Session, videoclase_id: int) -> models.VideoclasesData | None:
    return db.query(models.VideoclasesData).filter(models.VideoclasesData.id == videoclase_id).first()


def list_videoclases(db: Session) -> list[models.VideoclasesData]:
    return db.query(models.VideoclasesData).order_by(models.VideoclasesData.created_at.desc()).all()


def update_videoclase(db: Session, videoclase_id: int, payload: schemas.VideoclaseUpdate) -> models.VideoclasesData:
    videoclase = get_videoclase(db, videoclase_id)
    if not videoclase:
        return None

    for field, value in payload.model_dump(exclude_unset=True).items():
        setattr(videoclase, field, value)

    db.commit()
    db.refresh(videoclase)
    return videoclase


def delete_videoclase(db: Session, videoclase_id: int) -> bool:
    videoclase = get_videoclase(db, videoclase_id)
    if not videoclase:
        return False

    db.delete(videoclase)
    db.commit()
    return True


# Videos Cortos Data CRUD operations
def create_video_corto(db: Session, payload: schemas.VideoCortoCreate) -> models.VideosCortosData:
    entity = models.VideosCortosData(**payload.model_dump())
    db.add(entity)
    db.commit()
    db.refresh(entity)
    return entity


def get_video_corto(db: Session, video_corto_id: int) -> models.VideosCortosData | None:
    return db.query(models.VideosCortosData).filter(models.VideosCortosData.id == video_corto_id).first()


def list_videos_cortos(db: Session) -> list[models.VideosCortosData]:
    return db.query(models.VideosCortosData).order_by(models.VideosCortosData.created_at.desc()).all()


def update_video_corto(db: Session, video_corto_id: int, payload: schemas.VideoCortoUpdate) -> models.VideosCortosData:
    video_corto = get_video_corto(db, video_corto_id)
    if not video_corto:
        return None

    for field, value in payload.model_dump(exclude_unset=True).items():
        setattr(video_corto, field, value)

    db.commit()
    db.refresh(video_corto)
    return video_corto


def delete_video_corto(db: Session, video_corto_id: int) -> bool:
    video_corto = get_video_corto(db, video_corto_id)
    if not video_corto:
        return False

    db.delete(video_corto)
    db.commit()
    return True


# Notas Clinicas Data CRUD operations
def create_nota_clinica(db: Session, payload: schemas.NotaClinicaCreate, images_data: List[dict] = None) -> models.NotasClinicasData:
    entity = models.NotasClinicasData(
        title=payload.title,
        especialidad=payload.especialidad,
        sistema=payload.sistema,
        tema=payload.tema,
        content=payload.content,
        images=images_data or []
    )
    db.add(entity)
    db.commit()
    db.refresh(entity)
    return entity


def get_nota_clinica(db: Session, nota_clinica_id: int) -> models.NotasClinicasData | None:
    return db.query(models.NotasClinicasData).filter(models.NotasClinicasData.id == nota_clinica_id).first()


def list_notas_clinicas(db: Session) -> list[models.NotasClinicasData]:
    return db.query(models.NotasClinicasData).order_by(models.NotasClinicasData.created_at.desc()).all()


def update_nota_clinica(db: Session, nota_clinica_id: int, payload: schemas.NotaClinicaUpdate) -> models.NotasClinicasData:
    nota_clinica = get_nota_clinica(db, nota_clinica_id)
    if not nota_clinica:
        return None

    for field, value in payload.model_dump(exclude_unset=True).items():
        if value is not None:
            setattr(nota_clinica, field, value)

    db.commit()
    db.refresh(nota_clinica)
    return nota_clinica


def delete_nota_clinica(db: Session, nota_clinica_id: int) -> bool:
    nota_clinica = get_nota_clinica(db, nota_clinica_id)
    if not nota_clinica:
        return False

    db.delete(nota_clinica)
    db.commit()
    return True


# Casos Clinicos Data CRUD operations
def create_casos_clinicos_from_json(db: Session, payload: schemas.CasoClinicoJsonUpload) -> List[models.CasosClinicosData]:
    """Create multiple clinical cases from JSON upload"""
    created_casos = []
    
    for caso_json in payload.casos:
        # Convert JSON structure to embedded format
        preguntas_data = [{
            "caso_clinico": caso_json.caso_clinico,
            "pregunta": caso_json.pregunta,
            "respuesta_correcta": caso_json.respuesta_correcta,
            "explicacion_general": caso_json.explicacion_general,
            "opciones": [
                {
                    "opcion": opcion.opcion,
                    "es_correcta": opcion.es_correcta,
                    "explicacion": opcion.explicacion
                }
                for opcion in caso_json.opciones
            ]
        }]
        
        caso_clinico = models.CasosClinicosData(
            title=payload.title or f"Caso Clínico {caso_json.id}",
            especialidad=payload.especialidad,
            sistema=payload.sistema,
            tema=payload.tema,
            descripcion=caso_json.caso_clinico,
            images=[],
            preguntas=preguntas_data
        )
        db.add(caso_clinico)
        db.commit()
        db.refresh(caso_clinico)
        created_casos.append(caso_clinico)
    
    return created_casos


def create_caso_clinico(db: Session, payload: schemas.CasoClinicoCreate, images_data: List[dict] = None, preguntas_data: List[dict] = None) -> models.CasosClinicosData:
    entity = models.CasosClinicosData(
        title=payload.title,
        especialidad=payload.especialidad,
        sistema=payload.sistema,
        tema=payload.tema,
        descripcion=payload.descripcion,
        images=images_data or [],
        preguntas=preguntas_data or []
    )
    db.add(entity)
    db.commit()
    db.refresh(entity)
    return entity


def get_caso_clinico(db: Session, caso_clinico_id: int) -> models.CasosClinicosData | None:
    return db.query(models.CasosClinicosData).filter(models.CasosClinicosData.id == caso_clinico_id).first()


def list_casos_clinicos(db: Session) -> list[models.CasosClinicosData]:
    return db.query(models.CasosClinicosData).order_by(models.CasosClinicosData.created_at.desc()).all()


def update_caso_clinico(db: Session, caso_clinico_id: int, payload: schemas.CasoClinicoUpdate) -> models.CasosClinicosData:
    caso_clinico = get_caso_clinico(db, caso_clinico_id)
    if not caso_clinico:
        return None

    for field, value in payload.model_dump(exclude_unset=True).items():
        if value is not None:
            setattr(caso_clinico, field, value)

    db.commit()
    db.refresh(caso_clinico)
    return caso_clinico


def delete_caso_clinico(db: Session, caso_clinico_id: int) -> bool:
    caso_clinico = get_caso_clinico(db, caso_clinico_id)
    if not caso_clinico:
        return False

    db.delete(caso_clinico)
    db.commit()
    return True


# Cuestionarios Data CRUD operations
def create_cuestionarios_from_json(db: Session, payload: schemas.CuestionarioJsonUpload) -> List[models.CuestionariosData]:
    """Create multiple questionnaires from JSON upload (new structure)"""
    created_cuestionarios = []

    for cuestionario_json in payload.cuestionarios:
        # Convert JSON structure to embedded format
        preguntas_data = [{
            "pregunta": cuestionario_json.pregunta,
            "respuesta_correcta": cuestionario_json.respuesta_correcta,
            "explicacion_general": cuestionario_json.explicacion_general,
            "opciones": [
                {
                    "opcion": opcion.opcion,
                    "es_correcta": opcion.es_correcta,
                    "explicacion": opcion.explicacion
                }
                for opcion in cuestionario_json.opciones
            ]
        }]

        cuestionario = models.CuestionariosData(
            title=payload.title or f"Cuestionario {cuestionario_json.id}",
            especialidad=payload.especialidad,
            sistema=payload.sistema,
            tema=payload.tema,
            preguntas=preguntas_data
        )
        db.add(cuestionario)
        created_cuestionarios.append(cuestionario)

    db.commit()
    for cuestionario in created_cuestionarios:
        db.refresh(cuestionario)

    return created_cuestionarios





def create_cuestionario(db: Session, payload: schemas.CuestionarioCreate) -> models.CuestionariosData:
    entity = models.CuestionariosData(**payload.model_dump())
    db.add(entity)
    db.commit()
    db.refresh(entity)
    return entity


def get_cuestionario(db: Session, cuestionario_id: int) -> models.CuestionariosData | None:
    return db.query(models.CuestionariosData).filter(models.CuestionariosData.id == cuestionario_id).first()


def list_cuestionarios(db: Session) -> list[models.CuestionariosData]:
    return db.query(models.CuestionariosData).order_by(models.CuestionariosData.created_at.desc()).all()


def update_cuestionario(db: Session, cuestionario_id: int, payload: schemas.CuestionarioUpdate) -> models.CuestionariosData:
    cuestionario = get_cuestionario(db, cuestionario_id)
    if not cuestionario:
        return None

    for field, value in payload.model_dump(exclude_unset=True).items():
        if value is not None:
            setattr(cuestionario, field, value)

    db.commit()
    db.refresh(cuestionario)
    return cuestionario


def delete_cuestionario(db: Session, cuestionario_id: int) -> bool:
    cuestionario = get_cuestionario(db, cuestionario_id)
    if not cuestionario:
        return False

    db.delete(cuestionario)
    db.commit()
    return True


# Flashcards Data CRUD operations
def create_flashcards_from_json(db: Session, payload: schemas.FlashcardJsonUpload) -> List[models.FlashcardsData]:
    """Create multiple flashcards from JSON upload"""
    created_flashcards = []
    
    for flashcard_json in payload.flashcards:
        flashcard = models.FlashcardsData(
            title=payload.title or f"Flashcard {flashcard_json.id}",
            especialidad=payload.especialidad,
            sistema=payload.sistema,
            tema=payload.tema,
            question=flashcard_json.question,
            answer=flashcard_json.answer,
            explanation=flashcard_json.explanation,
            difficulty=flashcard_json.difficulty,
            category=flashcard_json.category,
            tags=flashcard_json.tags or []
        )
        db.add(flashcard)
        db.commit()
        db.refresh(flashcard)
        created_flashcards.append(flashcard)
    
    return created_flashcards


def create_flashcard(db: Session, payload: schemas.FlashcardCreate) -> models.FlashcardsData:
    entity = models.FlashcardsData(**payload.model_dump())
    db.add(entity)
    db.commit()
    db.refresh(entity)
    return entity


def get_flashcard(db: Session, flashcard_id: int) -> models.FlashcardsData | None:
    return db.query(models.FlashcardsData).filter(models.FlashcardsData.id == flashcard_id).first()


def list_flashcards(db: Session) -> list[models.FlashcardsData]:
    return db.query(models.FlashcardsData).order_by(models.FlashcardsData.created_at.desc()).all()


def update_flashcard(db: Session, flashcard_id: int, payload: schemas.FlashcardUpdate) -> models.FlashcardsData:
    flashcard = get_flashcard(db, flashcard_id)
    if not flashcard:
        return None

    for field, value in payload.model_dump(exclude_unset=True).items():
        if value is not None:
            setattr(flashcard, field, value)

    db.commit()
    db.refresh(flashcard)
    return flashcard


def delete_flashcard(db: Session, flashcard_id: int) -> bool:
    flashcard = get_flashcard(db, flashcard_id)
    if not flashcard:
        return False

    db.delete(flashcard)
    db.commit()
    return True


# Utility functions
def get_unique_especialidades(db: Session) -> list[str]:
    """Get all unique especialidades across all content types"""
    especialidades = set()
    
    # Get from all content tables
    for model in [models.VideoclasesData, models.VideosCortosData, models.NotasClinicasData, models.CasosClinicosData, models.CuestionariosData, models.FlashcardsData]:
        results = db.query(model.especialidad).distinct().all()
        especialidades.update([r[0] for r in results])
    
    return sorted(list(especialidades))


def get_sistemas_by_especialidad(db: Session, especialidad: str) -> list[str]:
    """Get all unique sistemas for a specific especialidad"""
    sistemas = set()
    
    # Get from all content tables
    for model in [models.VideoclasesData, models.VideosCortosData, models.NotasClinicasData, models.CasosClinicosData, models.CuestionariosData, models.FlashcardsData]:
        results = db.query(model.sistema).filter(model.especialidad == especialidad).distinct().all()
        sistemas.update([r[0] for r in results])
    
    return sorted(list(sistemas))


def get_temas_by_especialidad_sistema(db: Session, especialidad: str, sistema: str) -> list[str]:
    """Get all unique temas for a specific especialidad and sistema"""
    temas = set()
    
    # Get from all content tables
    for model in [models.VideoclasesData, models.VideosCortosData, models.NotasClinicasData, models.CasosClinicosData, models.CuestionariosData, models.FlashcardsData]:
        results = db.query(model.tema).filter(
            model.especialidad == especialidad,
            model.sistema == sistema
        ).distinct().all()
        temas.update([r[0] for r in results])
    
    return sorted(list(temas))


def list_content_by_especialidad_sistema_tema(db: Session, especialidad: str, sistema: str, tema: str) -> dict:
    """Get all content types for a specific especialidad, sistema and tema"""
    return {
        "videoclases": db.query(models.VideoclasesData).filter(
            models.VideoclasesData.especialidad == especialidad,
            models.VideoclasesData.sistema == sistema,
            models.VideoclasesData.tema == tema
        ).all(),
        "videos_cortos": db.query(models.VideosCortosData).filter(
            models.VideosCortosData.especialidad == especialidad,
            models.VideosCortosData.sistema == sistema,
            models.VideosCortosData.tema == tema
        ).all(),
        "notas_clinicas": db.query(models.NotasClinicasData).filter(
            models.NotasClinicasData.especialidad == especialidad,
            models.NotasClinicasData.sistema == sistema,
            models.NotasClinicasData.tema == tema
        ).all(),
        "casos_clinicos": db.query(models.CasosClinicosData).filter(
            models.CasosClinicosData.especialidad == especialidad,
            models.CasosClinicosData.sistema == sistema,
            models.CasosClinicosData.tema == tema
        ).all(),
        "cuestionarios": db.query(models.CuestionariosData).filter(
            models.CuestionariosData.especialidad == especialidad,
            models.CuestionariosData.sistema == sistema,
            models.CuestionariosData.tema == tema
        ).all(),
        "flashcards": db.query(models.FlashcardsData).filter(
            models.FlashcardsData.especialidad == especialidad,
            models.FlashcardsData.sistema == sistema,
            models.FlashcardsData.tema == tema
        ).all()
    }