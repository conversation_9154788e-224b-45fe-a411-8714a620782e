# Sistema de Flashcards

## Descripción
El sistema de flashcards permite crear y gestionar tarjetas de estudio para contenido médico educativo. Las flashcards incluyen pregunta, respuesta, explicación opcional, nivel de dificultad, categoría y tags.

## Características

### Backend
- **Modelo de datos**: Tabla `content_flashcards` con campos para pregunta, respuesta, explicación, dificultad, categoría y tags
- **API endpoints**: CRUD completo para flashcards individuales y carga masiva desde JSON
- **Validación**: Validación de estructura JSON y campos requeridos
- **Integración**: Incluido en el sistema de especialidades y temas existente

### Frontend
- **Interfaz dual**: Creación manual individual o carga masiva desde JSON
- **Formulario completo**: Campos para todos los atributos de la flashcard
- **Sistema de tags**: Añadir y remover tags dinámicamente
- **Lista visual**: Vista de todas las flashcards con filtros visuales por dificultad

## Estructura JSON para Carga Masiva

```json
[
  {
    "id": "1",
    "question": "¿Cuál es la función principal del corazón?",
    "answer": "Bombear sangre a través del sistema circulatorio",
    "explanation": "El corazón actúa como una bomba muscular...",
    "difficulty": "easy",
    "category": "Anatomía",
    "tags": ["cardiología", "anatomía", "fisiología"]
  }
]
```

### Campos Requeridos
- `id`: Identificador único (string)
- `question`: Pregunta de la flashcard (string)
- `answer`: Respuesta de la flashcard (string)

### Campos Opcionales
- `explanation`: Explicación detallada (string)
- `difficulty`: Nivel de dificultad - "easy", "medium", "hard" (string)
- `category`: Categoría temática (string)
- `tags`: Array de etiquetas (array de strings)

## API Endpoints

### Flashcards Individuales
- `POST /api/v1/upload-content/flashcards` - Crear flashcard individual
- `GET /api/v1/upload-content/flashcards` - Listar todas las flashcards
- `GET /api/v1/upload-content/flashcards/{id}` - Obtener flashcard específica
- `PUT /api/v1/upload-content/flashcards/{id}` - Actualizar flashcard
- `DELETE /api/v1/upload-content/flashcards/{id}` - Eliminar flashcard

### Carga Masiva
- `POST /api/v1/upload-content/flashcards/json` - Crear múltiples flashcards desde JSON

### Filtrado por Contenido
- `GET /api/v1/upload-content/content/{especialidad}/{tema}` - Incluye flashcards en la respuesta

## Uso del Sistema

### Carga Individual
1. Seleccionar especialidad, tema y título
2. Completar formulario con pregunta y respuesta (requeridos)
3. Añadir explicación, dificultad, categoría y tags (opcionales)
4. Hacer clic en "Crear Flashcard"

### Carga Masiva JSON
1. Seleccionar especialidad y tema
2. Pegar contenido JSON en el textarea
3. Hacer clic en "Crear desde JSON"
4. El sistema validará y creará todas las flashcards

### Gestión de Tags
- Escribir tag en el campo de entrada
- Presionar Enter o hacer clic en el botón "+"
- Hacer clic en la "X" del tag para eliminarlo

## Migración de Base de Datos

Para aplicar la migración que crea la tabla de flashcards:

```bash
cd server
alembic upgrade head
```

## Archivos Creados/Modificados

### Backend
- `server/app/upload_content/models.py` - Añadido modelo `Flashcard`
- `server/app/upload_content/schemas.py` - Añadidos schemas para flashcards
- `server/app/upload_content/crud.py` - Añadidas operaciones CRUD para flashcards
- `server/app/api/v1/endpoints/upload_content.py` - Añadidos endpoints API
- `server/alembic/versions/add_flashcards_table.py` - Migración de base de datos

### Frontend
- `client/app/upload/flashcards.tsx` - Componente principal de flashcards
- `client/app/upload/page.tsx` - Añadida pestaña de flashcards

### Ejemplos
- `sample_flashcards.json` - Archivo de ejemplo con flashcards de cardiología

## Características Técnicas

### Validación
- Campos requeridos: pregunta y respuesta
- Validación de formato JSON
- Validación de estructura de arrays y objetos

### Interfaz de Usuario
- Diseño responsivo con grid layout
- Indicadores visuales de dificultad con colores
- Sistema de badges para categorías y tags
- Textarea con fuente monoespaciada para JSON

### Integración
- Completamente integrado con el sistema de especialidades/temas existente
- Incluido en las consultas de contenido por especialidad/tema
- Consistente con el patrón de diseño de otros componentes

## Ejemplo de Uso

1. **Preparar JSON**: Usar `sample_flashcards.json` como referencia
2. **Seleccionar contexto**: Elegir "Medicina Interna" > "Cardiología"
3. **Pegar contenido**: Copiar y pegar el JSON en el textarea
4. **Crear flashcards**: Hacer clic en "Crear desde JSON"
5. **Verificar**: Las flashcards aparecerán en la lista lateral

El sistema está listo para uso inmediato una vez aplicada la migración de base de datos.