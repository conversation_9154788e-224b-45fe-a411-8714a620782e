"use client";

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Upload, HelpCircle, Trash2, Plus, X, Edit, Eye } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface EspecialidadTemaData {
  especialidad: string
  tema: string
  titulo: string
}

interface CuestionarioComponentProps {
  sharedData: EspecialidadTemaData
}

interface OpcionData {
  id: string;
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
  imagen_url?: string;
  imagen_path?: string;
}

interface PreguntaData {
  id: string;
  pregunta: string;
  respuesta_correcta: string;
  explicacion_general: string;
  imagen_url?: string;
  imagen_path?: string;
  opciones: OpcionData[];
}

interface OpcionRespuesta {
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
}

interface CuestionarioJson {
  id: string;
  pregunta: string;
  opciones: OpcionRespuesta[];
  respuesta_correcta: string;
  explicacion_general: string;
}

interface Cuestionario {
  id: number;
  title: string;
  especialidad: string;
  tema: string;
  created_at: string;
  preguntas: PreguntaData[];
}

// Helper function to get API base URL
function getApiBaseUrl(): string {
  const base = process.env.NEXT_PUBLIC_API_BASE_URL?.replace(/\/$/, '');
  return base || 'http://localhost:8000';
}

export default function CuestionarioComponent({ sharedData }: CuestionarioComponentProps) {
  // State management
  const [cuestionariosData, setCuestionariosData] = useState<PreguntaData[]>([]);
  const [selectedCuestionario, setSelectedCuestionario] = useState<PreguntaData | null>(null);
  const [selectedCuestionarioId, setSelectedCuestionarioId] = useState<number | null>(null);
  const [showDetailView, setShowDetailView] = useState(false);
  const [editingCuestionario, setEditingCuestionario] = useState<number | null>(null);
  const [cuestionarios, setCuestionarios] = useState<Cuestionario[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [jsonContent, setJsonContent] = useState('');
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());

  // Fetch cuestionarios when component mounts
  const fetchCuestionarios = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/cuestionarios`);
      if (!response.ok) throw new Error('Error al cargar los cuestionarios');
      const data = await response.json();
      setCuestionarios(data);
    } catch (err) {
      console.error('Error fetching cuestionarios:', err);
      setError('Error al cargar los cuestionarios');
    }
  };

  // Load cuestionarios on mount
  useEffect(() => {
    fetchCuestionarios();
  }, []);

  // New structure functions
  const addCuestionario = () => {
    const newCuestionario: PreguntaData = {
      id: Date.now().toString(),
      pregunta: '',
      respuesta_correcta: 'A',
      explicacion_general: '',
      opciones: [
        { id: '1', opcion: 'A. ', es_correcta: true, explicacion: '' },
        { id: '2', opcion: 'B. ', es_correcta: false, explicacion: '' },
        { id: '3', opcion: 'C. ', es_correcta: false, explicacion: '' },
        { id: '4', opcion: 'D. ', es_correcta: false, explicacion: '' }
      ]
    };
    setCuestionariosData([...cuestionariosData, newCuestionario]);
  };

  const removeCuestionario = (id: string) => {
    setCuestionariosData(cuestionariosData.filter(c => c.id !== id));
  };

  const updateCuestionario = (id: string, field: keyof PreguntaData, value: any) => {
    setCuestionariosData(cuestionariosData.map(c => 
      c.id === id ? { ...c, [field]: value } : c
    ));
  };

  const updateOpcion = (cuestionarioId: string, opcionId: string, field: keyof OpcionData, value: any) => {
    setCuestionariosData(cuestionariosData.map(c => 
      c.id === cuestionarioId 
        ? {
            ...c,
            opciones: c.opciones.map(o => 
              o.id === opcionId ? { ...o, [field]: value } : o
            )
          }
        : c
    ));
  };

  const setCorrectAnswer = (cuestionarioId: string, respuestaCorrecta: string) => {
    setCuestionariosData(cuestionariosData.map(c => 
      c.id === cuestionarioId 
        ? {
            ...c,
            respuesta_correcta: respuestaCorrecta,
            opciones: c.opciones.map(o => ({
              ...o,
              es_correcta: o.opcion.startsWith(respuestaCorrecta + '.')
            }))
          }
        : c
    ));
  };

  // CRUD functions for existing cuestionarios
  const updateCuestionarioInDB = async (cuestionarioId: number, updates: any) => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/cuestionarios/${cuestionarioId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Error al actualizar el cuestionario');
      }

      const updatedCuestionario = await response.json();
      
      // Update local state
      setCuestionarios(cuestionarios.map(c => 
        c.id === cuestionarioId ? updatedCuestionario : c
      ));
      
      return updatedCuestionario;
    } catch (err) {
      console.error('Error updating cuestionario:', err);
      setError('Error al actualizar el cuestionario');
      throw err;
    }
  };

  const deleteCuestionarioFromDB = async (cuestionarioId: number) => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/cuestionarios/${cuestionarioId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Error al eliminar el cuestionario');
      }

      // Update local state
      setCuestionarios(cuestionarios.filter(c => c.id !== cuestionarioId));
      
      if (selectedCuestionarioId === cuestionarioId) {
        setSelectedCuestionario(null);
        setSelectedCuestionarioId(null);
        setShowDetailView(false);
      }
    } catch (err) {
      console.error('Error deleting cuestionario:', err);
      setError('Error al eliminar el cuestionario');
    }
  };

  // Image upload functions
  const uploadImage = async (file: File, type: 'pregunta' | 'opcion', cuestionarioId: number, opcionId?: string) => {
    const uploadId = `${cuestionarioId}-${type}-${opcionId || 'pregunta'}`;
    setUploadingImages(prev => new Set([...prev, uploadId]));
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('content_type', 'cuestionarios');
      formData.append('content_id', cuestionarioId.toString());
      
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/upload-image`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Error al subir la imagen');
      }

      const result = await response.json();
      
      // Update the cuestionario with the new image URL
      const cuestionario = cuestionarios.find(c => c.id === cuestionarioId);
      if (cuestionario && cuestionario.preguntas.length > 0) {
        const pregunta = cuestionario.preguntas[0];
        
        if (type === 'pregunta') {
          pregunta.imagen_url = result.url;
          pregunta.imagen_path = result.path;
        } else if (type === 'opcion' && opcionId) {
          const opcion = pregunta.opciones.find(o => o.id === opcionId);
          if (opcion) {
            opcion.imagen_url = result.url;
            opcion.imagen_path = result.path;
          }
        }
        
        // Update in database
        await updateCuestionarioInDB(cuestionarioId, { preguntas: cuestionario.preguntas });
      }
      
      return result;
    } catch (err) {
      console.error('Error uploading image:', err);
      setError('Error al subir la imagen');
      throw err;
    } finally {
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(uploadId);
        return newSet;
      });
    }
  };

  const removeImage = async (cuestionarioId: number, type: 'pregunta' | 'opcion', opcionId?: string) => {
    try {
      const cuestionario = cuestionarios.find(c => c.id === cuestionarioId);
      if (cuestionario && cuestionario.preguntas.length > 0) {
        const pregunta = cuestionario.preguntas[0];
        
        if (type === 'pregunta') {
          pregunta.imagen_url = undefined;
          pregunta.imagen_path = undefined;
        } else if (type === 'opcion' && opcionId) {
          const opcion = pregunta.opciones.find(o => o.id === opcionId);
          if (opcion) {
            opcion.imagen_url = undefined;
            opcion.imagen_path = undefined;
          }
        }
        
        // Update in database
        await updateCuestionarioInDB(cuestionarioId, { preguntas: cuestionario.preguntas });
      }
    } catch (err) {
      console.error('Error removing image:', err);
      setError('Error al eliminar la imagen');
    }
  };

  const handleDelete = async (cuestionarioId: number) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este cuestionario?')) return;

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/cuestionarios/${cuestionarioId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar el cuestionario');

      setCuestionarios(cuestionarios.filter(c => c.id !== cuestionarioId));
      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error deleting cuestionario:', err);
      setError('Error al eliminar el cuestionario');
    }
  };

  // Handle JSON content submission
  const handleJsonSubmit = async () => {
    if (!jsonContent.trim()) {
      setError('Por favor ingrese el contenido JSON');
      return;
    }

    if (!sharedData.especialidad || !sharedData.tema) {
      setError('Por favor complete la información general (especialidad y tema) antes de procesar el JSON');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(false);

    try {
      // Validate JSON format first
      let parsedJson;
      try {
        parsedJson = JSON.parse(jsonContent);
      } catch (parseError) {
        throw new Error('Formato JSON inválido');
      }

      // Validate structure
      if (!Array.isArray(parsedJson) || parsedJson.length === 0) {
        throw new Error('El JSON debe contener una lista de cuestionarios');
      }

      // Validate required fields
      const requiredFields = ['id', 'pregunta', 'opciones', 'respuesta_correcta', 'explicacion_general'];
      for (const cuestionario of parsedJson) {
        for (const field of requiredFields) {
          if (!(field in cuestionario)) {
            throw new Error(`Campo requerido faltante: ${field}`);
          }
        }
      }

      const formData = new FormData();
      formData.append('json_content', jsonContent);
      formData.append('especialidad', sharedData.especialidad);
      formData.append('sistema', 'TuResiBo');
      formData.append('tema', sharedData.tema);
      if (sharedData.titulo) {
        formData.append('titulo', sharedData.titulo);
      }

      const response = await fetch(`${getApiBaseUrl()}/api/v1/upload-content/cuestionarios/json`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar el contenido JSON');
      }

      const result = await response.json();

      if (result.success) {
        setSuccess(true);
        setError(null);
        setJsonContent('');
        fetchCuestionarios();
      } else {
        throw new Error(result.message || 'Error al procesar el contenido JSON');
      }
    } catch (err) {
      console.error('Error processing JSON:', err);
      setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');
    } finally {
      setIsUploading(false);
    }
  };

  // Enhanced detailed view component with inline editing
  const DetailedView = ({ cuestionario, cuestionarioId, isEditing, onToggleEdit }: {
    cuestionario: PreguntaData;
    cuestionarioId: number;
    isEditing: boolean;
    onToggleEdit: () => void;
  }) => {
    const [localCuestionario, setLocalCuestionario] = useState<PreguntaData>(cuestionario);
    const [isSaving, setIsSaving] = useState(false);

    const handleSave = async () => {
      setIsSaving(true);
      try {
        await updateCuestionarioInDB(cuestionarioId, { preguntas: [localCuestionario] });
        onToggleEdit();
      } catch (err) {
        console.error('Error saving:', err);
      } finally {
        setIsSaving(false);
      }
    };

    const handleImageUpload = async (file: File, type: 'pregunta' | 'opcion', opcionId?: string) => {
      try {
        await uploadImage(file, type, cuestionarioId, opcionId);
        // Refresh the cuestionario data
        fetchCuestionarios();
      } catch (err) {
        console.error('Error uploading image:', err);
      }
    };

    const handleImageRemove = async (type: 'pregunta' | 'opcion', opcionId?: string) => {
      try {
        await removeImage(cuestionarioId, type, opcionId);
        // Refresh the cuestionario data
        fetchCuestionarios();
      } catch (err) {
        console.error('Error removing image:', err);
      }
    };

    return (
      <div className="space-y-6">
        {/* Header with Edit Controls */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Cuestionario Detallado</h2>
          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSaving ? 'Guardando...' : 'Guardar'}
                </Button>
                <Button
                  onClick={onToggleEdit}
                  variant="outline"
                >
                  Cancelar
                </Button>
              </>
            ) : (
              <Button
                onClick={onToggleEdit}
                variant="outline"
              >
                Editar
              </Button>
            )}
          </div>
        </div>

        {/* Question Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Pregunta</h3>
          {isEditing ? (
            <Textarea
              value={localCuestionario.pregunta}
              onChange={(e) => setLocalCuestionario({...localCuestionario, pregunta: e.target.value})}
              rows={3}
              className="mb-4"
            />
          ) : (
            <p className="text-gray-800 leading-relaxed mb-4">{cuestionario.pregunta}</p>
          )}

          {/* Image Upload/Display for Question */}
          <div className="space-y-2">
            {cuestionario.imagen_url ? (
              <div className="relative">
                <img
                  src={cuestionario.imagen_url}
                  alt="Imagen de la pregunta"
                  className="max-w-full h-auto rounded-lg border"
                />
                {isEditing && (
                  <Button
                    onClick={() => handleImageRemove('pregunta')}
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ) : isEditing && (
              <div>
                <Label className="text-sm font-medium">Agregar imagen a la pregunta</Label>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleImageUpload(file, 'pregunta');
                  }}
                  className="mt-1"
                />
              </div>
            )}
          </div>
        </div>
        {/* Options Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Opciones de Respuesta</h3>
          {(isEditing ? localCuestionario.opciones : cuestionario.opciones).map((opcion, index) => (
            <div
              key={`${cuestionarioId}-opcion-${opcion.id}-${index}`}
              className={`border rounded-lg p-4 ${
                opcion.es_correcta
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-200 bg-white'
              }`}
            >
              <div className="flex items-start gap-3">
                <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${
                  opcion.es_correcta
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-300 text-gray-700'
                }`}>
                  {String.fromCharCode(65 + index)}
                </div>
                <div className="flex-1 space-y-3">
                  {/* Option Text */}
                  {isEditing ? (
                    <Input
                      value={opcion.opcion}
                      onChange={(e) => {
                        const updatedOpciones = localCuestionario.opciones.map(o =>
                          o.id === opcion.id ? {...o, opcion: e.target.value} : o
                        );
                        setLocalCuestionario({...localCuestionario, opciones: updatedOpciones});
                      }}
                      className="font-medium"
                    />
                  ) : (
                    <p className="font-medium text-gray-900">{opcion.opcion}</p>
                  )}

                  {/* Option Explanation */}
                  <div className="bg-gray-50 border border-gray-200 rounded p-3">
                    {isEditing ? (
                      <Textarea
                        value={opcion.explicacion}
                        onChange={(e) => {
                          const updatedOpciones = localCuestionario.opciones.map(o =>
                            o.id === opcion.id ? {...o, explicacion: e.target.value} : o
                          );
                          setLocalCuestionario({...localCuestionario, opciones: updatedOpciones});
                        }}
                        rows={2}
                        className="text-sm"
                      />
                    ) : (
                      <p className="text-sm text-gray-700 leading-relaxed">
                        <strong>Explicación:</strong> {opcion.explicacion}
                      </p>
                    )}
                  </div>

                  {/* Option Image */}
                  <div className="space-y-2">
                    {opcion.imagen_url ? (
                      <div className="relative">
                        <img
                          src={opcion.imagen_url}
                          alt={`Imagen opción ${String.fromCharCode(65 + index)}`}
                          className="max-w-full h-auto rounded border"
                        />
                        {isEditing && (
                          <Button
                            onClick={() => handleImageRemove('opcion', opcion.id)}
                            variant="destructive"
                            size="sm"
                            className="absolute top-2 right-2"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ) : isEditing && (
                      <div>
                        <Label className="text-sm font-medium">Agregar imagen a la opción</Label>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'opcion', opcion.id);
                          }}
                          className="mt-1"
                        />
                      </div>
                    )}
                  </div>

                  {/* Correct Answer Radio */}
                  {isEditing && (
                    <div className="flex items-center gap-2">
                      <input
                        type="radio"
                        name={`correct-answer-${cuestionarioId}`}
                        checked={opcion.es_correcta}
                        onChange={() => {
                          const letter = String.fromCharCode(65 + index);
                          const updatedOpciones = localCuestionario.opciones.map(o => ({
                            ...o,
                            es_correcta: o.id === opcion.id
                          }));
                          setLocalCuestionario({
                            ...localCuestionario,
                            opciones: updatedOpciones,
                            respuesta_correcta: letter
                          });
                        }}
                        className="w-4 h-4"
                      />
                      <Label className="text-sm">Respuesta correcta</Label>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Correct Answer Indicator */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-semibold text-green-900 mb-2">Respuesta Correcta</h4>
          <p className="text-green-800">Opción {cuestionario.respuesta_correcta}</p>
        </div>

        {/* General Explanation */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-3">Explicación General</h3>
          {isEditing ? (
            <Textarea
              value={localCuestionario.explicacion_general}
              onChange={(e) => setLocalCuestionario({...localCuestionario, explicacion_general: e.target.value})}
              rows={3}
            />
          ) : (
            <p className="text-gray-800 leading-relaxed">{cuestionario.explicacion_general}</p>
          )}
        </div>
      </div>
    );
  };

  // Main component render
  if (showDetailView && selectedCuestionario && selectedCuestionarioId) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Button
          onClick={() => {
            setShowDetailView(false);
            setSelectedCuestionario(null);
            setSelectedCuestionarioId(null);
            setEditingCuestionario(null);
          }}
          variant="outline"
          className="mb-4"
        >
          ← Volver a la lista
        </Button>
        <DetailedView
          cuestionario={selectedCuestionario}
          cuestionarioId={selectedCuestionarioId}
          isEditing={editingCuestionario === selectedCuestionarioId}
          onToggleEdit={() => {
            if (editingCuestionario === selectedCuestionarioId) {
              setEditingCuestionario(null);
            } else {
              setEditingCuestionario(selectedCuestionarioId);
            }
          }}
        />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Form */}
      <div className="lg:col-span-2">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Plus className="h-5 w-5" />
              Nuevo Cuestionario
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* JSON Upload Section */}
            <div className="mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Upload className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium text-blue-900">Crear desde JSON</h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                Pega el contenido JSON para crear múltiples cuestionarios de una vez.
              </p>
              <div className="space-y-3">
                <Textarea
                  id="json-content"
                  placeholder="Pega aquí el contenido JSON..."
                  value={jsonContent}
                  onChange={(e) => setJsonContent(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <Button
                  type="button"
                  onClick={handleJsonSubmit}
                  disabled={!jsonContent.trim() || isUploading}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="h-4 w-4" />
                  {isUploading ? 'Procesando...' : 'Crear desde JSON'}
                </Button>
              </div>

              {/* JSON Format Example */}
              <details className="mt-3">
                <summary className="text-sm text-blue-700 cursor-pointer hover:text-blue-800">
                  Ver formato JSON requerido
                </summary>
                <div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto">
                  <pre>{`[
  {
    "id": "1",
    "pregunta": "¿Cuál es el tratamiento de primera línea para la hipertensión arterial?",
    "opciones": [
      {
        "opcion": "A. Inhibidores de la ECA (IECA)",
        "es_correcta": true,
        "explicacion": "Los IECA son considerados tratamiento de primera línea..."
      },
      {
        "opcion": "B. Betabloqueadores",
        "es_correcta": false,
        "explicacion": "Los betabloqueadores no son la primera opción..."
      }
    ],
    "respuesta_correcta": "A",
    "explicacion_general": "El manejo inicial de la hipertensión sigue las guías..."
  }
]`}</pre>
                </div>
              </details>
            </div>

            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">O crear manualmente</span>
              </div>
            </div>

            {/* Manual Creation Form */}
            <div className="mb-8 p-4 border border-green-200 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-4">
                <HelpCircle className="h-5 w-5 text-green-600" />
                <h3 className="font-medium text-green-900">Crear Cuestionario</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Cuestionarios</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCuestionario}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Agregar Cuestionario
                  </Button>
                </div>

                {cuestionariosData.map((cuestionario) => (
                  <Card key={cuestionario.id} className="p-4 bg-white">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Cuestionario {cuestionario.id}</Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCuestionario(cuestionario.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Question */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Pregunta</Label>
                        <Textarea
                          placeholder="Escriba la pregunta..."
                          value={cuestionario.pregunta}
                          onChange={(e) => updateCuestionario(cuestionario.id, 'pregunta', e.target.value)}
                          rows={3}
                        />
                      </div>

                      {/* Options */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Opciones</Label>
                        <div className="space-y-3">
                          {cuestionario.opciones.map((opcion, index) => (
                            <div key={`manual-${cuestionario.id}-opcion-${opcion.id}-${index}`} className="border rounded p-3 space-y-2">
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-sm">
                                  {String.fromCharCode(65 + index)}.
                                </span>
                                <Input
                                  placeholder={`Opción ${String.fromCharCode(65 + index)}`}
                                  value={opcion.opcion}
                                  onChange={(e) => updateOpcion(cuestionario.id, opcion.id, 'opcion', e.target.value)}
                                  className="flex-1"
                                />
                                <input
                                  type="radio"
                                  name={`correct-${cuestionario.id}`}
                                  checked={opcion.es_correcta}
                                  onChange={() => setCorrectAnswer(cuestionario.id, String.fromCharCode(65 + index))}
                                  className="w-4 h-4"
                                />
                              </div>
                              <Textarea
                                placeholder="Explicación de esta opción..."
                                value={opcion.explicacion}
                                onChange={(e) => updateOpcion(cuestionario.id, opcion.id, 'explicacion', e.target.value)}
                                rows={2}
                                className="text-sm"
                              />
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* General Explanation */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Explicación General</Label>
                        <Textarea
                          placeholder="Explicación general del cuestionario..."
                          value={cuestionario.explicacion_general}
                          onChange={(e) => updateCuestionario(cuestionario.id, 'explicacion_general', e.target.value)}
                          rows={3}
                        />
                      </div>
                    </div>
                  </Card>
                ))}

                {cuestionariosData.length > 0 && (
                  <Button
                    type="button"
                    onClick={() => {
                      // Convert to JSON and submit
                      const jsonData = JSON.stringify(cuestionariosData, null, 2);
                      setJsonContent(jsonData);
                      handleJsonSubmit();
                    }}
                    disabled={isUploading}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {isUploading ? 'Creando...' : 'Crear Cuestionarios'}
                  </Button>
                )}
              </div>
            </div>

            {/* Alerts */}
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="border-green-200 bg-green-50 mb-4">
                <AlertDescription className="text-green-800">
                  ¡Cuestionarios creados exitosamente!
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Cuestionarios List */}
      <div className="lg:col-span-1">
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg">Cuestionarios ({cuestionarios.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {cuestionarios.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <HelpCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">No hay cuestionarios aún</p>
                </div>
              ) : (
                cuestionarios.map((cuestionario) => (
                  <div key={cuestionario.id} className="group border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{cuestionario.title}</h3>
                        <div className="flex items-center gap-1 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {cuestionario.especialidad}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {cuestionario.preguntas.length} preguntas
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1 truncate">{cuestionario.tema}</p>
                      </div>
                      <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            if (cuestionario.preguntas.length > 0) {
                              setSelectedCuestionario(cuestionario.preguntas[0]);
                              setSelectedCuestionarioId(cuestionario.id);
                              setShowDetailView(true);
                            }
                          }}
                          className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(cuestionario.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
