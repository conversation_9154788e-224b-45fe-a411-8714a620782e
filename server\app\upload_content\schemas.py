from __future__ import annotations

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel


# Videoclases Data Schemas
class VideoclaseBase(BaseModel):
    title: str
    especialidad: str
    sistema: str
    tema: str
    url: Optional[str] = None
    file_path: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[int] = None
    description: Optional[str] = None


class VideoclaseCreate(VideoclaseBase):
    pass


class VideoclaseUpdate(BaseModel):
    title: Optional[str] = None
    especialidad: Optional[str] = None
    sistema: Optional[str] = None
    tema: Optional[str] = None
    url: Optional[str] = None
    file_path: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[int] = None
    description: Optional[str] = None


class VideoclaseOut(VideoclaseBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Videos Cortos Data Schemas
class VideoCortoBase(BaseModel):
    title: str
    especialidad: str
    sistema: str
    tema: str
    url: Optional[str] = None
    file_path: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[int] = None
    description: Optional[str] = None


class VideoCortoCreate(VideoCortoBase):
    pass


class VideoCortoUpdate(BaseModel):
    title: Optional[str] = None
    especialidad: Optional[str] = None
    sistema: Optional[str] = None
    tema: Optional[str] = None
    url: Optional[str] = None
    file_path: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[int] = None
    description: Optional[str] = None


class VideoCortoOut(VideoCortoBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Notas Clinicas Data Schemas (Unified with embedded images)
class NotaClinicaBase(BaseModel):
    title: str
    especialidad: str
    sistema: str
    tema: str
    content: str
    images: Optional[List[dict]] = None


class NotaClinicaCreate(NotaClinicaBase):
    pass


class NotaClinicaUpdate(BaseModel):
    title: Optional[str] = None
    especialidad: Optional[str] = None
    sistema: Optional[str] = None
    tema: Optional[str] = None
    content: Optional[str] = None
    images: Optional[List[dict]] = None


class NotaClinicaOut(NotaClinicaBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Casos Clinicos Data Schemas (Unified with embedded questions and options)
class CasoClinicoBase(BaseModel):
    title: str
    especialidad: str
    sistema: str
    tema: str
    descripcion: str
    images: Optional[List[dict]] = None
    preguntas: List[dict] = []  # All questions and options embedded as JSON


class CasoClinicoCreate(CasoClinicoBase):
    pass


class CasoClinicoUpdate(BaseModel):
    title: Optional[str] = None
    especialidad: Optional[str] = None
    sistema: Optional[str] = None
    tema: Optional[str] = None
    descripcion: Optional[str] = None
    images: Optional[List[dict]] = None
    preguntas: Optional[List[dict]] = None


class CasoClinicoOut(CasoClinicoBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Cuestionarios Data Schemas (Unified with embedded questions)
class CuestionarioBase(BaseModel):
    title: str
    especialidad: str
    sistema: str
    tema: str
    preguntas: List[dict] = []  # All questions and answers embedded as JSON


class CuestionarioCreate(CuestionarioBase):
    pass


class CuestionarioUpdate(BaseModel):
    title: Optional[str] = None
    especialidad: Optional[str] = None
    sistema: Optional[str] = None
    tema: Optional[str] = None
    preguntas: Optional[List[dict]] = None


class CuestionarioOut(CuestionarioBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Flashcards Data Schemas
class FlashcardBase(BaseModel):
    title: str
    especialidad: str
    sistema: str
    tema: str
    question: str
    answer: str
    explanation: Optional[str] = None
    difficulty: Optional[str] = None  # easy, medium, hard
    category: Optional[str] = None
    tags: Optional[List[str]] = None


class FlashcardCreate(FlashcardBase):
    pass


class FlashcardUpdate(BaseModel):
    title: Optional[str] = None
    especialidad: Optional[str] = None
    sistema: Optional[str] = None
    tema: Optional[str] = None
    question: Optional[str] = None
    answer: Optional[str] = None
    explanation: Optional[str] = None
    difficulty: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None


class FlashcardOut(FlashcardBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# Additional schemas for API compatibility
class Respuesta(BaseModel):
    text: str
    is_correct: bool


class PreguntaCreate(BaseModel):
    text: str
    answers: List[Respuesta]
    explanation: Optional[str] = None


# JSON Upload Schemas
class OpcionJsonCreate(BaseModel):
    opcion: str
    es_correcta: bool
    explicacion: str


class CasoClinicoJsonCreate(BaseModel):
    id: str
    caso_clinico: str
    pregunta: str
    opciones: List[OpcionJsonCreate]
    respuesta_correcta: str
    explicacion_general: str


class CasoClinicoJsonUpload(BaseModel):
    casos: List[CasoClinicoJsonCreate]
    especialidad: str
    sistema: str
    tema: str
    title: Optional[str] = None


class RespuestaJsonCreate(BaseModel):
    text: str
    is_correct: bool


class PreguntaJsonCreate(BaseModel):
    id: str
    text: str
    answers: List[RespuestaJsonCreate]
    explanation: Optional[str] = None


# New Cuestionario JSON Schemas (similar to CasosClinicosData)
class OpcionCuestionarioJsonCreate(BaseModel):
    opcion: str
    es_correcta: bool
    explicacion: str


class CuestionarioJsonCreate(BaseModel):
    id: str
    pregunta: str
    opciones: List[OpcionCuestionarioJsonCreate]
    respuesta_correcta: str
    explicacion_general: str


class CuestionarioJsonUpload(BaseModel):
    cuestionarios: List[CuestionarioJsonCreate]
    especialidad: str
    sistema: str
    tema: str
    title: Optional[str] = None





class FlashcardJsonCreate(BaseModel):
    id: str
    question: str
    answer: str
    explanation: Optional[str] = None
    difficulty: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None


class FlashcardJsonItem(BaseModel):
    id: str
    question: str
    answer: str
    explanation: Optional[str] = None
    difficulty: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None


class FlashcardJsonUpload(BaseModel):
    flashcards: List[FlashcardJsonItem]
    especialidad: str
    sistema: str
    tema: str
    title: Optional[str] = None




