{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vpuyGpnRGjkFou8C30vySVISi+U2geFoS/CwcDPvqwM=", "__NEXT_PREVIEW_MODE_ID": "5f1a74dbd095b8a5980e6db7cc8cdc04", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "50d4f0fc9dcf97bad4afac68f5de34344449136eb91976d3789762ff62ba8313", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a5be17fe5f37ed88efb2a4a50a510126ad78dfc4f877e47f5a1a8b63b9924265"}}}, "functions": {}, "sortedMiddleware": ["/"]}