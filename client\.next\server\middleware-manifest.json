{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vpuyGpnRGjkFou8C30vySVISi+U2geFoS/CwcDPvqwM=", "__NEXT_PREVIEW_MODE_ID": "710732f1ac40c06d2ec500aeae70d811", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "748a6e334cf1b64843bef3e5f3ffeb621c2f201461cba3927039ffaa42b83524", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aab3e145b656a683a79a85cad2b9b52091373887a18a620914b2318fa5762837"}}}, "functions": {}, "sortedMiddleware": ["/"]}