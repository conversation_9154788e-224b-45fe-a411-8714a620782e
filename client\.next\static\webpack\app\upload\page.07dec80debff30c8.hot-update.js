"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/upload/page",{

/***/ "(app-pages-browser)/./app/upload/flashcards.tsx":
/*!***********************************!*\
  !*** ./app/upload/flashcards.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashcardsComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Edit,Eye,Plus,Save,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Helper function to get API base URL\nfunction getApiBaseUrl() {\n    var _process_env_NEXT_PUBLIC_API_BASE_URL;\n    const base = (_process_env_NEXT_PUBLIC_API_BASE_URL = \"http://localhost:8000/\") === null || _process_env_NEXT_PUBLIC_API_BASE_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_BASE_URL.replace(/\\/$/, '');\n    return base || 'http://localhost:8000';\n}\nfunction FlashcardsComponent(param) {\n    let {} = param;\n    _s();\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jsonContent, setJsonContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '',\n        pregunta: '',\n        respuesta: '',\n        etiquetas: []\n    });\n    const [tagInput, setTagInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [editingId, setEditingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingFlashcard, setViewingFlashcard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch flashcards\n    const fetchFlashcards = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/flashcards\"));\n            if (!response.ok) throw new Error('Error al cargar las flashcards');\n            const data = await response.json();\n            setFlashcards(data);\n        } catch (err) {\n            console.error('Error fetching flashcards:', err);\n            setError('Error al cargar las flashcards');\n        }\n    };\n    // Load flashcards on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardsComponent.useEffect\": ()=>{\n            fetchFlashcards();\n        }\n    }[\"FlashcardsComponent.useEffect\"], []);\n    // Handle JSON content submission\n    const handleJsonSubmit = async ()=>{\n        if (!jsonContent.trim()) {\n            setError('Por favor ingrese el contenido JSON');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            const formData = new FormData();\n            formData.append('json_content', jsonContent);\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/flashcards/json\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Error al procesar el contenido JSON');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                setError(null);\n                setJsonContent('');\n                fetchFlashcards();\n            } else {\n                throw new Error(result.message || 'Error al procesar el contenido JSON');\n            }\n        } catch (err) {\n            console.error('Error processing JSON:', err);\n            setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    // Handle manual form submission\n    const handleFormSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.id.trim() || !formData.pregunta.trim() || !formData.respuesta.trim()) {\n            setError('Por favor complete el ID, pregunta y respuesta');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            const submitData = new FormData();\n            submitData.append('id', formData.id);\n            submitData.append('pregunta', formData.pregunta);\n            submitData.append('respuesta', formData.respuesta);\n            if (formData.etiquetas.length > 0) submitData.append('etiquetas', JSON.stringify(formData.etiquetas));\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/flashcards\"), {\n                method: 'POST',\n                body: submitData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Error al crear la flashcard');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                resetForm();\n                fetchFlashcards();\n            } else {\n                throw new Error(result.message || 'Error al crear la flashcard');\n            }\n        } catch (err) {\n            console.error('Error:', err);\n            setError(err instanceof Error ? err.message : 'Error al crear la flashcard');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    // Add tag\n    const addTag = ()=>{\n        if (tagInput.trim() && !formData.etiquetas.includes(tagInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    etiquetas: [\n                        ...prev.etiquetas,\n                        tagInput.trim()\n                    ]\n                }));\n            setTagInput('');\n        }\n    };\n    // Remove tag\n    const removeTag = (tagToRemove)=>{\n        setFormData((prev)=>({\n                ...prev,\n                etiquetas: prev.etiquetas.filter((tag)=>tag !== tagToRemove)\n            }));\n    };\n    // Reset form\n    const resetForm = ()=>{\n        setFormData({\n            id: '',\n            pregunta: '',\n            respuesta: '',\n            etiquetas: []\n        });\n        setTagInput('');\n        setJsonContent('');\n        setError(null);\n        setSuccess(false);\n        setEditingId(null);\n    };\n    // Handle delete\n    const handleDelete = async (flashcardId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar esta flashcard?')) return;\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/flashcards/\").concat(flashcardId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Error al eliminar la flashcard');\n            setFlashcards(flashcards.filter((f)=>f.id !== flashcardId));\n            setSuccess(true);\n            setTimeout(()=>setSuccess(false), 3000);\n        } catch (err) {\n            console.error('Error deleting flashcard:', err);\n            setError('Error al eliminar la flashcard');\n        }\n    };\n    // Handle edit\n    const handleEdit = (flashcard)=>{\n        setFormData({\n            id: flashcard.id,\n            pregunta: flashcard.pregunta,\n            respuesta: flashcard.respuesta,\n            etiquetas: flashcard.etiquetas || []\n        });\n        setEditingId(flashcard.id);\n    };\n    // Handle update\n    const handleUpdate = async (e)=>{\n        e.preventDefault();\n        if (!editingId) return;\n        setIsUploading(true);\n        setError(null);\n        try {\n            const submitData = new FormData();\n            if (formData.id !== editingId) submitData.append('id', formData.id);\n            submitData.append('pregunta', formData.pregunta);\n            submitData.append('respuesta', formData.respuesta);\n            if (formData.etiquetas.length > 0) submitData.append('etiquetas', JSON.stringify(formData.etiquetas));\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/flashcards/\").concat(editingId), {\n                method: 'PUT',\n                body: submitData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Error al actualizar la flashcard');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                resetForm();\n                fetchFlashcards();\n            } else {\n                throw new Error(result.message || 'Error al actualizar la flashcard');\n            }\n        } catch (err) {\n            console.error('Error:', err);\n            setError(err instanceof Error ? err.message : 'Error al actualizar la flashcard');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"Flashcards\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            variant: \"outline\",\n                            className: \"ml-2\",\n                            children: [\n                                flashcards.length,\n                                \" tarjetas\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-0 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            className: \"flex items-center gap-2 text-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                editingId ? 'Editar Flashcard' : 'Nueva Flashcard'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Crear desde JSON\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700 mb-3\",\n                                        children: \"Pega el contenido JSON para crear m\\xfaltiples flashcards de una vez.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"json-content\",\n                                                placeholder: \"Pega aqu\\xed el contenido JSON...\",\n                                                value: jsonContent,\n                                                onChange: (e)=>setJsonContent(e.target.value),\n                                                rows: 8,\n                                                className: \"font-mono text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                onClick: handleJsonSubmit,\n                                                disabled: !jsonContent.trim() || isUploading,\n                                                className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isUploading ? 'Procesando...' : 'Crear desde JSON'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"text-sm text-blue-700 cursor-pointer hover:text-blue-800\",\n                                                children: \"Ver formato JSON requerido\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    children: '[\\n  {\\n    \"id\": \"hta_fc_001\",\\n    \"pregunta\": \"\\xbfCu\\xe1l es la definici\\xf3n de hipertensi\\xf3n arterial seg\\xfan la OMS?\",\\n    \"respuesta\": \"Presi\\xf3n arterial sist\\xf3lica ≥ 140 mmHg o diast\\xf3lica ≥ 90 mmHg en al menos dos mediciones en d\\xedas diferentes.\",\\n    \"etiquetas\": [\"definici\\xf3n\", \"diagn\\xf3stico\", \"OMS\"]\\n  }\\n]'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"w-full border-t\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-xs uppercase\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white px-2 text-gray-500\",\n                                            children: \"O crear manualmente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: editingId ? handleUpdate : handleFormSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"id\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"id\",\n                                                placeholder: \"ej: hta_fc_001\",\n                                                value: formData.id,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            id: e.target.value\n                                                        })),\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"pregunta\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Pregunta\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"pregunta\",\n                                                placeholder: \"\\xbfCu\\xe1l es la definici\\xf3n de hipertensi\\xf3n arterial seg\\xfan la OMS?\",\n                                                value: formData.pregunta,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            pregunta: e.target.value\n                                                        })),\n                                                rows: 3,\n                                                className: \"resize-none\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"respuesta\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Respuesta\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                id: \"respuesta\",\n                                                placeholder: \"Presi\\xf3n arterial sist\\xf3lica ≥ 140 mmHg o diast\\xf3lica ≥ 90 mmHg en al menos dos mediciones en d\\xedas diferentes.\",\n                                                value: formData.respuesta,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            respuesta: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"resize-none\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"Puedes incluir im\\xe1genes usando markdown: ![descripci\\xf3n](url)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Etiquetas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        placeholder: \"Agregar etiqueta...\",\n                                                        value: tagInput,\n                                                        onChange: (e)=>setTagInput(e.target.value),\n                                                        onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addTag())\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"button\",\n                                                        onClick: addTag,\n                                                        variant: \"outline\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.etiquetas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mt-2\",\n                                                children: formData.etiquetas.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            tag,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 cursor-pointer\",\n                                                                onClick: ()=>removeTag(tag)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                        variant: \"destructive\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this),\n                                    success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                        className: \"border-green-200 bg-green-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                            className: \"text-green-800\",\n                                            children: [\n                                                \"\\xa1Flashcard \",\n                                                editingId ? 'actualizada' : 'creada',\n                                                \" exitosamente!\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2 pt-4\",\n                                        children: [\n                                            editingId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                onClick: resetForm,\n                                                disabled: isUploading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Cancelar\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                disabled: isUploading,\n                                                className: \"min-w-[120px]\",\n                                                children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        editingId ? 'Actualizando...' : 'Creando...'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        editingId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 36\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 67\n                                                        }, this),\n                                                        editingId ? 'Actualizar' : 'Crear',\n                                                        \" Flashcard\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"border-0 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                        className: \"pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Lista de Flashcards\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"No hay flashcards a\\xfan\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, this) : flashcards.map((flashcard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group border rounded-lg p-4 hover:bg-gray-50 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs font-mono\",\n                                                                children: flashcard.id\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            flashcard.etiquetas && flashcard.etiquetas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    flashcard.etiquetas.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs\",\n                                                                            children: tag\n                                                                        }, index, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 31\n                                                                        }, this)),\n                                                                    flashcard.etiquetas.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            flashcard.etiquetas.length - 3\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-900 mb-1 line-clamp-2\",\n                                                        children: flashcard.pregunta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 line-clamp-2\",\n                                                        children: flashcard.respuesta\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTrigger, {\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-8 w-8 p-0\",\n                                                                    onClick: ()=>setViewingFlashcard(flashcard),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                                                                className: \"max-w-2xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"font-mono\",\n                                                                                    children: flashcard.id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                    lineNumber: 540,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                \"Flashcard\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium text-sm text-gray-700 mb-2\",\n                                                                                        children: \"Pregunta:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                        lineNumber: 548,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-gray-900\",\n                                                                                        children: flashcard.pregunta\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                        lineNumber: 549,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                lineNumber: 547,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium text-sm text-gray-700 mb-2\",\n                                                                                        children: \"Respuesta:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                        lineNumber: 552,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"prose prose-sm max-w-none\",\n                                                                                        children: flashcard.respuesta.split('\\n').map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                children: line\n                                                                                            }, index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                                lineNumber: 555,\n                                                                                                columnNumber: 35\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                        lineNumber: 553,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                lineNumber: 551,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            flashcard.etiquetas && flashcard.etiquetas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium text-sm text-gray-700 mb-2\",\n                                                                                        children: \"Etiquetas:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                        lineNumber: 561,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1\",\n                                                                                        children: flashcard.etiquetas.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                                variant: \"outline\",\n                                                                                                className: \"text-xs\",\n                                                                                                children: tag\n                                                                                            }, index, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                                lineNumber: 564,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                        lineNumber: 562,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                                lineNumber: 560,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleEdit(flashcard),\n                                                        className: \"h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>handleDelete(flashcard.id),\n                                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Edit_Eye_Plus_Save_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 19\n                                    }, this)\n                                }, flashcard.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n                lineNumber: 483,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\flashcards.tsx\",\n        lineNumber: 284,\n        columnNumber: 5\n    }, this);\n}\n_s(FlashcardsComponent, \"aznsi+F4+SFZ3/E85fuLZf+xOCs=\");\n_c = FlashcardsComponent;\nvar _c;\n$RefreshReg$(_c, \"FlashcardsComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/upload/flashcards.tsx\n"));

/***/ })

});