"use client"

import { useState, useEffect } from 'react'
import { Button } from './button'
import { Input } from './input'
import { Label } from './label'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu'
import { Heart, Scissors, Baby, Shield } from 'lucide-react'

interface EspecialidadTemaData {
  especialidad: string
  sistema: string
  tema: string
  titulo: string
}

interface EspecialidadTemaSelectorProps {
  value: EspecialidadTemaData
  onChange: (data: EspecialidadTemaData) => void
  disabled?: boolean
}

// Medical topics data from temario.tsx
const medicalTopicsBySystem = {
  "Medicina Interna": {
    icon: Heart,
    color: "blue" as const,
    systems: {
      "Medicina de Urgencias": [
        "1. Alcohol y alcoholismo",
        "24. Estado de choque",
        "39. Intoxicaciones (opiodes, cocaina y nicotina)",
        "43. <PERSON><PERSON><PERSON><PERSON> y picaduras",
      ],
      "Hematología": [
        "2. Anemia",
        "40. Leucemias y linfomas Diagnóstico inicial",
        "64. Tromboembolia venosa",
      ],
      "Infectología": [
        "3. Tratamiento y profilaxis de la infecciones bacterianas",
        "31. Infecciones de tejidos blandos",
        "47. Osteomielitis",
        "48. Paludismo",
        "56. Síndrome de inmunodeficiencia adquirida",
      ],
      "Reumatología": [
        "4. Artritis reumatoide",
        "5. Artropatía por cristales: gota",
        "41. Lupus eritematoso generalizado",
        "46. Osteoartrosis",
        "65. Vasculitis",
      ],
      "Neumología": [
        "6. Cáncer de pulmón",
        "11. Crisis asmática",
        "16. Derrame pleural",
        "22. Enfermedad pulmonar obstructiva Crónica",
        "32. Infecciones respiratorias altas en el adulto",
        "37. Insuficiencia respiratoria aguda",
        "44. Neumonía",
        "51. Rinitis alérgica",
      ],
      "Cardiología": [
        "7. Cardiopatía isquémica",
        "13. Crisis hipertensiva",
        "20. Endocarditis",
        "29. Hipertensión arterial sistémica",
        "33. Insuficiencia cardiaca",
        "50. Reanimación cardiopulmonar",
        "52. Síncope",
      ],
      "Neurología": [
        "8. Cefaleas",
        "12. Crisis convulsivas",
        "14. Delirio",
        "23. Epilepsia",
        "25. Evento vascular cerebral",
        "42. Meningitis aguda",
        "53. Síndrome confusional agudo (delirium)",
        "55. Síndrome de Guillain-Barré",
        "57. Síndrome demencial",
      ],
      "Endocrinología": [
        "9. Complicaciones agudas de diabetes mellitus",
        "18. Diabetes mellitus",
        "19. Trastornos del metabolismo de las lipoproteinas",
        "45. Obesidad en el adulto",
        "49. Patología de tiroides",
        "58. Síndrome metabólico",
      ],
      "Oftalmología": ["10. Conjuntivitis aguda y crónica"],
      "Dermatología": ["15. Dermatitis por contacto"],
      "Nefrología": [
        "17. Desequilibrios hidroelectrolítico y ácido-base",
        "30. Infección de vías urinarias",
        "36. Insuficiencia renal aguda y crónica",
        "59. Síndrome nefrítico",
        "60. Síndrome nefrótico",
      ],
      "Gastroenterología": [
        "21. Enfermedad inflamatoria intestinal",
        "26. Gastroenteritis en el adulto",
        "27. Hemorragia de tubo digestivo",
        "28. Hepatopatías virales",
        "34. Hepatitis viral aguda",
        "35. Insuficiencia hepática crónica: cirrosis hepática",
        "54. Síndrome de colon irritable",
      ],
      "Psiquiatría": [
        "38. Trastornos mentales",
        "61. Trastorno de ansiedad",
        "62. Trastorno depresivo mayor",
        "63. Trastornos de la alimentación (anorexia y bulimia)",
      ],
    },
  },
  "Cirugía General": {
    icon: Scissors,
    color: "red" as const,
    systems: {
      "Trauma y Cuidados Críticos": [
        "1a. Choque",
        "1b. Traumatismos",
        "1c. Quemaduras",
        "1d. Hemostasia y transfusión",
        "1e. Manejo de líquidos y electrolitos",
      ],
      "Abdomen Agudo": [
        "2a. Apendicitis",
        "2b. Vesícula biliar y sistema biliar extrahepatico",
        "2c. Colon recto y ano",
        "2d. Páncreas",
        "2e. Ulcera péptica perforada",
      ],
      "Esófago": [
        "3a.i. Acalasia",
        "3a.ii. Enfermedad por reflujo gastroesofágico",
        "3a.iii. Esófago de Barrett y adenocarcinoma",
      ],
      "Estómago": [
        "3b.i. Enfermedad ulcero péptica",
        "3b.ii. Neoplasias malignas gástricas",
      ],
      "Intestino Delgado": [
        "3c.i. Obstrucción del intestino delgado",
        "3c.ii. Enfermedad de Crohn",
        "3c.iii. Divertículo de Meckel",
        "3c.iv. Isquemia mesentérica",
      ],
      "Intestino Grueso, Recto y Ano": [
        "3d.i. Enfermedad diverticular",
        "3d.ii. Adenocarcinoma y pólipos",
        "3d.iii. Obstrucción del intestino grueso",
        "3d.iv. Colitis ulcerosa",
        "3d.v. Absceso perianal",
        "3d.vi. Hemorroides",
      ],
      "Hígado y Vías Biliares": [
        "3e.i. Quistes hepáticos",
        "3e.ii. Absceso hepático",
        "3e.iii. Tumores hepáticos malignos",
        "3e.iv. Colecistitis crónica y aguda",
        "3e.v. Tumores de vesícula y conductos biliares",
      ],
      "Páncreas": [
        "3f.i. Neoplasias pancreáticas",
      ],
      "Cirugía Ortopédica": [
        "4a.i. Fracturas expuestas",
        "4a.ii. Luxaciones de codo, cadera, rodilla",
        "4a.iii. Fracturas de cadera",
      ],
      "Urología": [
        "4b.i. Cáncer vesical, renal, testículos, próstata",
        "4b.ii. Hiperplasia prostática benigna",
        "4b.iii. Litiasis urinaria",
        "4b.iv. Torsión testicular",
      ],
      "Pared Abdominal": ["4c.i. Hernias de la pared abdominal"],
    },
  },
  "Ginecología y Obstetricia": {
    icon: Baby,
    color: "pink" as const,
    systems: {
      "Obstetricia - Control Prenatal": [
        "1. Control prenatal",
        "2. Embarazo Múltiple",
        "9. Diabetes gestacional",
      ],
      "Obstetricia - Complicaciones del Embarazo": [
        "3. Hemorragias de la primera mitad del embarazo",
        "4. Hemorragias de la segunda mitad del embarazo",
        "5. Síndrome de rotura prematura de membranas ovulares",
        "8. Estados Hipertensivos del embarazo",
      ],
      "Obstetricia - Parto y Puerperio": [
        "6. Parto Prematuro",
        "7. Embarazo prolongado",
        "10. Trabajo de parto",
        "11. Mecanismo del parto en general",
        "12. Atención del parto",
        "13. Lactancia materna",
        "14. Puerperio normal",
        "15. Puerperio patológico",
      ],
      "Ginecología - Infecciones": [
        "16. Infecciones de transmisión sexual",
        "24. Enfermedad pélvica inflamatoria",
        "25. Infecciones vaginales",
      ],
      "Ginecología - Patología Uterina": [
        "17. Patología del endometrio",
        "19. Patología cervicouterina",
        "23. Endometriosis",
        "26. Miomatosis uterina",
        "31. Metrorragias",
      ],
      "Ginecología - Trastornos Hormonales": [
        "18. Amenorrea",
        "22. Climaterio y menopausia",
      ],
      "Ginecología - Patología Mamaria": [
        "20. Cáncer de mama",
        "27. Patología benigna de mama",
      ],
      "Ginecología - Patología Ovárica": [
        "21. Cáncer de ovario",
        "28. Patología benigna de ovario",
        "32. Síndrome de ovario poliquístico",
      ],
      "Ginecología - Salud Reproductiva": [
        "29. Salud Sexual y reproductiva",
        "30. Aborto",
      ],
    },
  },
  "Pediatría": {
    icon: Baby,
    color: "green" as const,
    systems: {
      "Gastroenterología Pediátrica": [
        "1. Apendicitis aguda",
        "11. Estenosis Hipertrófica de píloro",
        "16. Gastroenteritis en niños",
        "24. Enfermedad por reflujo gastroesofágico",
      ],
      "Neumología Pediátrica": [
        "2. Asma",
        "23. Enfermedades del aparato respiratorio",
      ],
      "Neonatología": [
        "3. Anemia en el Recién Nacido",
        "7. Enfermedades Neonatales",
        "12. Hipotiroidismo congénito",
        "13. Ictericia e hiperbilirrubinemia en el Recién Nacido",
        "25. Infecciones del recién nacido",
      ],
      "Pediatría General": [
        "4. El primer año",
        "21. La alimentación de los lactantes, los niños y los adolescentes sanos",
      ],
      "Neurología Pediátrica": [
        "5. Crisis Febriles",
        "26. Traumatismo craneoencefálico",
        "31. Síndrome de Guillain-Barré",
      ],
      "Endocrinología Pediátrica": ["6. Diabetes mellitus tipo 1"],
      "Infectología Pediátrica": [
        "8. Sarampión",
        "10. Hepatitis Víricas",
        "20. Meningitis bacteriana aguda",
        "28. Infecciones de transmisión sexual",
        "29. Tétanos",
      ],
      "Cardiología Pediátrica": ["9. Fiebre reumática"],
      "Otorrinolaringología Pediátrica": ["14. Sinusitis"],
      "Nefrología Pediátrica": [
        "15. Infecciones del tracto urinario",
        "30. Síndrome Nefrótico",
      ],
      "Medicina de Urgencias Pediátrica": [
        "17. Intoxicaciones",
        "19. Trastornos electrolíticos y ácidobásicos",
        "27. Shock",
      ],
      "Hematología Pediátrica": ["18. Leucemias"],
      "Ortopedia Pediátrica": ["22. Displasia del desarrollo de la cadera"],
    },
  },
  "Programas Nacionales de Salud": {
    icon: Shield,
    color: "purple" as const,
    systems: {
      "Inmunizaciones": ["1. Programa Ampliado de Inmunización – PAI"],
      "Enfermedades Vectoriales": [
        "2. Programa de Vigilancia y Control del Dengue",
        "3. Programa de Vigilancia y Control de Chagas",
        "4. Programa de Vigilancia y Control de Malaria",
        "6. Programa de Vigilancia y Control de Leishmaniasis",
      ],
      "Enfermedades Infecciosas": [
        "5. Programa de Vigilancia y Control de ITS/VIH/SIDA",
        "10. Programa de Vigilancia y Control Nacional de Tuberculosis",
        "11. Programa Nacional de Vigilancia y Control de Influenza",
      ],
      "Enfermedades Crónicas": ["7. Programa de Vigilancia y Control de Lepra"],
      "Zoonosis": [
        "8. Programa de Vigilancia y Control de Zoonosis y Rabia",
        "9. Programa de Enfermedades Transmitidas por Roedores",
      ],
      "Accidentes y Envenenamientos": ["12. Programa de Vigilancia y Control Accidentes para Ofidios y Ponzoñosos"],
    },
  },
} as const

export function EspecialidadTemaSelector({ value, onChange, disabled = false }: EspecialidadTemaSelectorProps) {
  const [especialidades] = useState<string[]>(Object.keys(medicalTopicsBySystem))
  const [sistemas, setSistemas] = useState<string[]>([])
  const [temas, setTemas] = useState<string[]>([])

  // Update sistemas when especialidad changes
  useEffect(() => {
    if (value.especialidad && medicalTopicsBySystem[value.especialidad as keyof typeof medicalTopicsBySystem]) {
      const specialty = medicalTopicsBySystem[value.especialidad as keyof typeof medicalTopicsBySystem]
      setSistemas(Object.keys(specialty.systems))
    } else {
      setSistemas([])
    }
  }, [value.especialidad])

  // Update temas when sistema changes
  useEffect(() => {
    if (value.especialidad && value.sistema && medicalTopicsBySystem[value.especialidad as keyof typeof medicalTopicsBySystem]) {
      const specialty = medicalTopicsBySystem[value.especialidad as keyof typeof medicalTopicsBySystem]
      const systemTopics = specialty.systems[value.sistema as keyof typeof specialty.systems] || []
      setTemas(systemTopics)
    } else {
      setTemas([])
    }
  }, [value.especialidad, value.sistema])

  const handleEspecialidadChange = (especialidad: string) => {
    onChange({
      ...value,
      especialidad,
      sistema: '', // Reset sistema when especialidad changes
      tema: '' // Reset tema when especialidad changes
    })
  }

  const handleSistemaChange = (sistema: string) => {
    onChange({
      ...value,
      sistema,
      tema: '' // Reset tema when sistema changes
    })
  }

  const handleTemaChange = (tema: string) => {
    onChange({
      ...value,
      tema
    })
  }

  const handleTituloChange = (titulo: string) => {
    onChange({
      ...value,
      titulo
    })
  }

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold text-gray-900">Información General</h3>
      <p className="text-sm text-gray-600">
        Esta información se aplicará a todo el contenido que subas en esta sesión.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="especialidad">Especialidad</Label>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full justify-between h-10"
                disabled={disabled}
              >
                {value.especialidad ? (
                  <div className="flex items-center gap-2">
                    {medicalTopicsBySystem[value.especialidad as keyof typeof medicalTopicsBySystem] && (
                      <div className="w-4 h-4">
                        {(() => {
                          const IconComponent = medicalTopicsBySystem[value.especialidad as keyof typeof medicalTopicsBySystem].icon
                          return <IconComponent className="h-4 w-4" />
                        })()} 
                      </div>
                    )}
                    <span>{value.especialidad}</span>
                  </div>
                ) : (
                  'Selecciona una especialidad'
                )}
                <span className="ml-2">▼</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full min-w-[300px]">
              {especialidades.map((esp) => {
                const IconComponent = medicalTopicsBySystem[esp as keyof typeof medicalTopicsBySystem].icon
                return (
                  <DropdownMenuItem 
                    key={esp} 
                    onSelect={() => handleEspecialidadChange(esp)}
                    className="cursor-pointer"
                  >
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-4 w-4" />
                      <span>{esp}</span>
                    </div>
                  </DropdownMenuItem>
                )
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-2">
          <Label htmlFor="sistema">Sistema</Label>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full justify-between h-10"
                disabled={disabled || !value.especialidad}
              >
                {value.sistema && value.sistema.length > 30 ? (
                  <span title={value.sistema}>{value.sistema.substring(0, 27)}...</span>
                ) : (
                  value.sistema || 'Selecciona un sistema'
                )}
                <span className="ml-2">▼</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full min-w-[300px] max-h-60 overflow-y-auto">
              {sistemas.map((sistema) => (
                <DropdownMenuItem 
                  key={sistema} 
                  onSelect={() => handleSistemaChange(sistema)}
                  className="cursor-pointer whitespace-normal p-3"
                >
                  <span className="text-sm leading-relaxed">{sistema}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-2">
          <Label htmlFor="tema">Tema</Label>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full justify-between h-10"
                disabled={disabled || !value.sistema}
              >
                {value.tema && value.tema.length > 30 ? (
                  <span title={value.tema}>{value.tema.substring(0, 27)}...</span>
                ) : (
                  value.tema || 'Selecciona un tema'
                )}
                <span className="ml-2">▼</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full min-w-[400px] max-h-60 overflow-y-auto">
              {temas.map((tema) => (
                <DropdownMenuItem 
                  key={tema} 
                  onSelect={() => handleTemaChange(tema)}
                  className="cursor-pointer whitespace-normal p-3"
                >
                  <span className="text-sm leading-relaxed">{tema}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="titulo">Título</Label>
        <Input
          id="titulo"
          value={value.titulo}
          onChange={(e) => handleTituloChange(e.target.value)}
          placeholder="Ingresa el título del contenido"
          disabled={disabled}
        />
      </div>

      {value.especialidad && value.sistema && value.tema && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-800">
            <strong>Configuración actual:</strong> {value.especialidad} → {value.sistema} → {value.tema}
            {value.titulo && ` → ${value.titulo}`}
          </p>
        </div>
      )}

      {value.especialidad && value.sistema && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="text-sm text-blue-800">
            <strong>Temas disponibles en {value.sistema}:</strong>
            <div className="mt-2 grid grid-cols-1 gap-1">
              {temas.map((tema) => (
                <div key={tema} className="text-xs bg-blue-100 px-2 py-1 rounded">
                  {tema}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}