"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/upload/page",{

/***/ "(app-pages-browser)/./app/upload/cuestionario.tsx":
/*!*************************************!*\
  !*** ./app/upload/cuestionario.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CuestionarioComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,HelpCircle,Plus,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Helper function to get API base URL\nfunction getApiBaseUrl() {\n    var _process_env_NEXT_PUBLIC_API_BASE_URL;\n    const base = (_process_env_NEXT_PUBLIC_API_BASE_URL = \"http://localhost:8000/\") === null || _process_env_NEXT_PUBLIC_API_BASE_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_BASE_URL.replace(/\\/$/, '');\n    return base || 'http://localhost:8000';\n}\nfunction CuestionarioComponent(param) {\n    let { sharedData } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    // State management\n    const [cuestionariosData, setCuestionariosData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCuestionario, setSelectedCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCuestionarioId, setSelectedCuestionarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailView, setShowDetailView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCuestionario, setEditingCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cuestionarios, setCuestionarios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jsonContent, setJsonContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Fetch cuestionarios when component mounts\n    const fetchCuestionarios = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios\"));\n            if (!response.ok) throw new Error('Error al cargar los cuestionarios');\n            const data = await response.json();\n            setCuestionarios(data);\n        } catch (err) {\n            console.error('Error fetching cuestionarios:', err);\n            setError('Error al cargar los cuestionarios');\n        }\n    };\n    // Load cuestionarios on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CuestionarioComponent.useEffect\": ()=>{\n            fetchCuestionarios();\n        }\n    }[\"CuestionarioComponent.useEffect\"], []);\n    // New structure functions\n    const addCuestionario = ()=>{\n        const newCuestionario = {\n            id: Date.now().toString(),\n            pregunta: '',\n            respuesta_correcta: 'A',\n            explicacion_general: '',\n            opciones: [\n                {\n                    id: '1',\n                    opcion: 'A. ',\n                    es_correcta: true,\n                    explicacion: ''\n                },\n                {\n                    id: '2',\n                    opcion: 'B. ',\n                    es_correcta: false,\n                    explicacion: ''\n                },\n                {\n                    id: '3',\n                    opcion: 'C. ',\n                    es_correcta: false,\n                    explicacion: ''\n                },\n                {\n                    id: '4',\n                    opcion: 'D. ',\n                    es_correcta: false,\n                    explicacion: ''\n                }\n            ]\n        };\n        setCuestionariosData([\n            ...cuestionariosData,\n            newCuestionario\n        ]);\n    };\n    const removeCuestionario = (id)=>{\n        setCuestionariosData(cuestionariosData.filter((c)=>c.id !== id));\n    };\n    const updateCuestionario = (id, field, value)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === id ? {\n                ...c,\n                [field]: value\n            } : c));\n    };\n    const updateOpcion = (cuestionarioId, opcionId, field, value)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === cuestionarioId ? {\n                ...c,\n                opciones: c.opciones.map((o)=>o.id === opcionId ? {\n                        ...o,\n                        [field]: value\n                    } : o)\n            } : c));\n    };\n    const setCorrectAnswer = (cuestionarioId, respuestaCorrecta)=>{\n        setCuestionariosData(cuestionariosData.map((c)=>c.id === cuestionarioId ? {\n                ...c,\n                respuesta_correcta: respuestaCorrecta,\n                opciones: c.opciones.map((o)=>({\n                        ...o,\n                        es_correcta: o.opcion.startsWith(respuestaCorrecta + '.')\n                    }))\n            } : c));\n    };\n    // CRUD functions for existing cuestionarios\n    const updateCuestionarioInDB = async (cuestionarioId, updates)=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                throw new Error('Error al actualizar el cuestionario');\n            }\n            const updatedCuestionario = await response.json();\n            // Update local state\n            setCuestionarios(cuestionarios.map((c)=>c.id === cuestionarioId ? updatedCuestionario : c));\n            return updatedCuestionario;\n        } catch (err) {\n            console.error('Error updating cuestionario:', err);\n            setError('Error al actualizar el cuestionario');\n            throw err;\n        }\n    };\n    const deleteCuestionarioFromDB = async (cuestionarioId)=>{\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error('Error al eliminar el cuestionario');\n            }\n            // Update local state\n            setCuestionarios(cuestionarios.filter((c)=>c.id !== cuestionarioId));\n            if (selectedCuestionario && selectedCuestionario.id === cuestionarioId.toString()) {\n                setSelectedCuestionario(null);\n                setShowDetailView(false);\n            }\n        } catch (err) {\n            console.error('Error deleting cuestionario:', err);\n            setError('Error al eliminar el cuestionario');\n        }\n    };\n    // Image upload functions\n    const uploadImage = async (file, type, cuestionarioId, opcionId)=>{\n        const uploadId = \"\".concat(cuestionarioId, \"-\").concat(type, \"-\").concat(opcionId || 'pregunta');\n        setUploadingImages((prev)=>new Set([\n                ...prev,\n                uploadId\n            ]));\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('content_type', 'cuestionarios');\n            formData.append('content_id', cuestionarioId.toString());\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/upload-image\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error('Error al subir la imagen');\n            }\n            const result = await response.json();\n            // Update the cuestionario with the new image URL\n            const cuestionario = cuestionarios.find((c)=>c.id === cuestionarioId);\n            if (cuestionario && cuestionario.preguntas.length > 0) {\n                const pregunta = cuestionario.preguntas[0];\n                if (type === 'pregunta') {\n                    pregunta.imagen_url = result.url;\n                    pregunta.imagen_path = result.path;\n                } else if (type === 'opcion' && opcionId) {\n                    const opcion = pregunta.opciones.find((o)=>o.id === opcionId);\n                    if (opcion) {\n                        opcion.imagen_url = result.url;\n                        opcion.imagen_path = result.path;\n                    }\n                }\n                // Update in database\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: cuestionario.preguntas\n                });\n            }\n            return result;\n        } catch (err) {\n            console.error('Error uploading image:', err);\n            setError('Error al subir la imagen');\n            throw err;\n        } finally{\n            setUploadingImages((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(uploadId);\n                return newSet;\n            });\n        }\n    };\n    const removeImage = async (cuestionarioId, type, opcionId)=>{\n        try {\n            const cuestionario = cuestionarios.find((c)=>c.id === cuestionarioId);\n            if (cuestionario && cuestionario.preguntas.length > 0) {\n                const pregunta = cuestionario.preguntas[0];\n                if (type === 'pregunta') {\n                    pregunta.imagen_url = undefined;\n                    pregunta.imagen_path = undefined;\n                } else if (type === 'opcion' && opcionId) {\n                    const opcion = pregunta.opciones.find((o)=>o.id === opcionId);\n                    if (opcion) {\n                        opcion.imagen_url = undefined;\n                        opcion.imagen_path = undefined;\n                    }\n                }\n                // Update in database\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: cuestionario.preguntas\n                });\n            }\n        } catch (err) {\n            console.error('Error removing image:', err);\n            setError('Error al eliminar la imagen');\n        }\n    };\n    const handleDelete = async (cuestionarioId)=>{\n        if (!confirm('¿Estás seguro de que quieres eliminar este cuestionario?')) return;\n        try {\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/\").concat(cuestionarioId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Error al eliminar el cuestionario');\n            setCuestionarios(cuestionarios.filter((c)=>c.id !== cuestionarioId));\n            setSuccess(true);\n            setTimeout(()=>setSuccess(false), 3000);\n        } catch (err) {\n            console.error('Error deleting cuestionario:', err);\n            setError('Error al eliminar el cuestionario');\n        }\n    };\n    // Handle JSON content submission\n    const handleJsonSubmit = async ()=>{\n        if (!jsonContent.trim()) {\n            setError('Por favor ingrese el contenido JSON');\n            return;\n        }\n        if (!sharedData.especialidad || !sharedData.tema) {\n            setError('Por favor complete la información general (especialidad y tema) antes de procesar el JSON');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(false);\n        try {\n            // Validate JSON format first\n            let parsedJson;\n            try {\n                parsedJson = JSON.parse(jsonContent);\n            } catch (parseError) {\n                throw new Error('Formato JSON inválido');\n            }\n            // Validate structure\n            if (!Array.isArray(parsedJson) || parsedJson.length === 0) {\n                throw new Error('El JSON debe contener una lista de cuestionarios');\n            }\n            // Validate required fields\n            const requiredFields = [\n                'id',\n                'pregunta',\n                'opciones',\n                'respuesta_correcta',\n                'explicacion_general'\n            ];\n            for (const cuestionario of parsedJson){\n                for (const field of requiredFields){\n                    if (!(field in cuestionario)) {\n                        throw new Error(\"Campo requerido faltante: \".concat(field));\n                    }\n                }\n            }\n            const formData = new FormData();\n            formData.append('json_content', jsonContent);\n            formData.append('especialidad', sharedData.especialidad);\n            formData.append('sistema', 'TuResiBo');\n            formData.append('tema', sharedData.tema);\n            if (sharedData.titulo) {\n                formData.append('titulo', sharedData.titulo);\n            }\n            const response = await fetch(\"\".concat(getApiBaseUrl(), \"/api/v1/upload-content/cuestionarios/json\"), {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.detail || 'Error al procesar el contenido JSON');\n            }\n            const result = await response.json();\n            if (result.success) {\n                setSuccess(true);\n                setError(null);\n                setJsonContent('');\n                fetchCuestionarios();\n            } else {\n                throw new Error(result.message || 'Error al procesar el contenido JSON');\n            }\n        } catch (err) {\n            console.error('Error processing JSON:', err);\n            setError(err instanceof Error ? err.message : 'Error al procesar el contenido JSON');\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    // Enhanced detailed view component with inline editing\n    const DetailedView = (param)=>{\n        let { cuestionario, cuestionarioId, isEditing, onToggleEdit } = param;\n        _s1();\n        const [localCuestionario, setLocalCuestionario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(cuestionario);\n        const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const handleSave = async ()=>{\n            setIsSaving(true);\n            try {\n                await updateCuestionarioInDB(cuestionarioId, {\n                    preguntas: [\n                        localCuestionario\n                    ]\n                });\n                onToggleEdit();\n            } catch (err) {\n                console.error('Error saving:', err);\n            } finally{\n                setIsSaving(false);\n            }\n        };\n        const handleImageUpload = async (file, type, opcionId)=>{\n            try {\n                await uploadImage(file, type, cuestionarioId, opcionId);\n                // Refresh the cuestionario data\n                fetchCuestionarios();\n            } catch (err) {\n                console.error('Error uploading image:', err);\n            }\n        };\n        const handleImageRemove = async (type, opcionId)=>{\n            try {\n                await removeImage(cuestionarioId, type, opcionId);\n                // Refresh the cuestionario data\n                fetchCuestionarios();\n            } catch (err) {\n                console.error('Error removing image:', err);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"Cuestionario Detallado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: handleSave,\n                                        disabled: isSaving,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: isSaving ? 'Guardando...' : 'Guardar'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: onToggleEdit,\n                                        variant: \"outline\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: onToggleEdit,\n                                variant: \"outline\",\n                                children: \"Editar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-blue-900 mb-3\",\n                            children: \"Pregunta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            value: localCuestionario.pregunta,\n                            onChange: (e)=>setLocalCuestionario({\n                                    ...localCuestionario,\n                                    pregunta: e.target.value\n                                }),\n                            rows: 3,\n                            className: \"mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-800 leading-relaxed mb-4\",\n                            children: cuestionario.pregunta\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: cuestionario.imagen_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: cuestionario.imagen_url,\n                                        alt: \"Imagen de la pregunta\",\n                                        className: \"max-w-full h-auto rounded-lg border\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 17\n                                    }, this),\n                                    isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>handleImageRemove('pregunta'),\n                                        variant: \"destructive\",\n                                        size: \"sm\",\n                                        className: \"absolute top-2 right-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, this) : isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Agregar imagen a la pregunta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        type: \"file\",\n                                        accept: \"image/*\",\n                                        onChange: (e)=>{\n                                            var _e_target_files;\n                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                            if (file) handleImageUpload(file, 'pregunta');\n                                        },\n                                        className: \"mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900\",\n                            children: \"Opciones de Respuesta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        (isEditing ? localCuestionario.opciones : cuestionario.opciones).map((opcion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4 \".concat(opcion.es_correcta ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-white'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium \".concat(opcion.es_correcta ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-700'),\n                                            children: String.fromCharCode(65 + index)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 space-y-3\",\n                                            children: [\n                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    value: opcion.opcion,\n                                                    onChange: (e)=>{\n                                                        const updatedOpciones = localCuestionario.opciones.map((o)=>o.id === opcion.id ? {\n                                                                ...o,\n                                                                opcion: e.target.value\n                                                            } : o);\n                                                        setLocalCuestionario({\n                                                            ...localCuestionario,\n                                                            opciones: updatedOpciones\n                                                        });\n                                                    },\n                                                    className: \"font-medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: opcion.opcion\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 border border-gray-200 rounded p-3\",\n                                                    children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                        value: opcion.explicacion,\n                                                        onChange: (e)=>{\n                                                            const updatedOpciones = localCuestionario.opciones.map((o)=>o.id === opcion.id ? {\n                                                                    ...o,\n                                                                    explicacion: e.target.value\n                                                                } : o);\n                                                            setLocalCuestionario({\n                                                                ...localCuestionario,\n                                                                opciones: updatedOpciones\n                                                            });\n                                                        },\n                                                        rows: 2,\n                                                        className: \"text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-700 leading-relaxed\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Explicaci\\xf3n:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            opcion.explicacion\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: opcion.imagen_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: opcion.imagen_url,\n                                                                alt: \"Imagen opci\\xf3n \".concat(String.fromCharCode(65 + index)),\n                                                                className: \"max-w-full h-auto rounded border\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: ()=>handleImageRemove('opcion', opcion.id),\n                                                                variant: \"destructive\",\n                                                                size: \"sm\",\n                                                                className: \"absolute top-2 right-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 23\n                                                    }, this) : isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Agregar imagen a la opci\\xf3n\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleImageUpload(file, 'opcion', opcion.id);\n                                                                },\n                                                                className: \"mt-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"radio\",\n                                                            name: \"correct-answer-\".concat(cuestionarioId),\n                                                            checked: opcion.es_correcta,\n                                                            onChange: ()=>{\n                                                                const letter = String.fromCharCode(65 + index);\n                                                                const updatedOpciones = localCuestionario.opciones.map((o)=>({\n                                                                        ...o,\n                                                                        es_correcta: o.id === opcion.id\n                                                                    }));\n                                                                setLocalCuestionario({\n                                                                    ...localCuestionario,\n                                                                    opciones: updatedOpciones,\n                                                                    respuesta_correcta: letter\n                                                                });\n                                                            },\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            className: \"text-sm\",\n                                                            children: \"Respuesta correcta\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this)\n                            }, opcion.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-green-900 mb-2\",\n                            children: \"Respuesta Correcta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800\",\n                            children: [\n                                \"Opci\\xf3n \",\n                                cuestionario.respuesta_correcta\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-yellow-900 mb-3\",\n                            children: \"Explicaci\\xf3n General\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                            value: localCuestionario.explicacion_general,\n                            onChange: (e)=>setLocalCuestionario({\n                                    ...localCuestionario,\n                                    explicacion_general: e.target.value\n                                }),\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-800 leading-relaxed\",\n                            children: cuestionario.explicacion_general\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(DetailedView, \"M9DyeJGnuD9oeGWL64ONYbhHnEY=\");\n    // Main component render\n    if (showDetailView && selectedCuestionario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: ()=>setShowDetailView(false),\n                    variant: \"outline\",\n                    className: \"mb-4\",\n                    children: \"← Volver a la lista\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DetailedView, {\n                    cuestionario: selectedCuestionario,\n                    cuestionarioId: parseInt(selectedCuestionario.id),\n                    isEditing: editingCuestionario === parseInt(selectedCuestionario.id),\n                    onToggleEdit: ()=>{\n                        if (editingCuestionario === parseInt(selectedCuestionario.id)) {\n                            setEditingCuestionario(null);\n                        } else {\n                            setEditingCuestionario(parseInt(selectedCuestionario.id));\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 672,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n            lineNumber: 664,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border-0 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"flex items-center gap-2 text-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Nuevo Cuestionario\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-blue-900\",\n                                                    children: \"Crear desde JSON\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mb-3\",\n                                            children: \"Pega el contenido JSON para crear m\\xfaltiples cuestionarios de una vez.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"json-content\",\n                                                    placeholder: \"Pega aqu\\xed el contenido JSON...\",\n                                                    value: jsonContent,\n                                                    onChange: (e)=>setJsonContent(e.target.value),\n                                                    rows: 8,\n                                                    className: \"font-mono text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"button\",\n                                                    onClick: handleJsonSubmit,\n                                                    disabled: !jsonContent.trim() || isUploading,\n                                                    className: \"flex items-center gap-2 bg-blue-600 hover:bg-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isUploading ? 'Procesando...' : 'Crear desde JSON'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            className: \"mt-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    className: \"text-sm text-blue-700 cursor-pointer hover:text-blue-800\",\n                                                    children: \"Ver formato JSON requerido\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 p-3 bg-gray-50 rounded text-xs font-mono overflow-x-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        children: '[\\n  {\\n    \"id\": \"1\",\\n    \"pregunta\": \"\\xbfCu\\xe1l es el tratamiento de primera l\\xednea para la hipertensi\\xf3n arterial?\",\\n    \"opciones\": [\\n      {\\n        \"opcion\": \"A. Inhibidores de la ECA (IECA)\",\\n        \"es_correcta\": true,\\n        \"explicacion\": \"Los IECA son considerados tratamiento de primera l\\xednea...\"\\n      },\\n      {\\n        \"opcion\": \"B. Betabloqueadores\",\\n        \"es_correcta\": false,\\n        \"explicacion\": \"Los betabloqueadores no son la primera opci\\xf3n...\"\\n      }\\n    ],\\n    \"respuesta_correcta\": \"A\",\\n    \"explicacion_general\": \"El manejo inicial de la hipertensi\\xf3n sigue las gu\\xedas...\"\\n  }\\n]'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-full border-t\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex justify-center text-xs uppercase\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-white px-2 text-gray-500\",\n                                                children: \"O crear manualmente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-4 border border-green-200 bg-green-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-green-900\",\n                                                    children: \"Crear Cuestionario\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Cuestionarios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            type: \"button\",\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: addCuestionario,\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Agregar Cuestionario\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 17\n                                                }, this),\n                                                cuestionariosData.map((cuestionario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                                        className: \"p-4 bg-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"Cuestionario \",\n                                                                                cuestionario.id\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeCuestionario(cuestionario.id),\n                                                                            className: \"text-red-600 hover:text-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Pregunta\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                            placeholder: \"Escriba la pregunta...\",\n                                                                            value: cuestionario.pregunta,\n                                                                            onChange: (e)=>updateCuestionario(cuestionario.id, 'pregunta', e.target.value),\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Opciones\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: cuestionario.opciones.map((opcion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"border rounded p-3 space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-medium text-sm\",\n                                                                                                    children: [\n                                                                                                        String.fromCharCode(65 + index),\n                                                                                                        \".\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 824,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    placeholder: \"Opci\\xf3n \".concat(String.fromCharCode(65 + index)),\n                                                                                                    value: opcion.opcion,\n                                                                                                    onChange: (e)=>updateOpcion(cuestionario.id, opcion.id, 'opcion', e.target.value),\n                                                                                                    className: \"flex-1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 827,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"radio\",\n                                                                                                    name: \"correct-\".concat(cuestionario.id),\n                                                                                                    checked: opcion.es_correcta,\n                                                                                                    onChange: ()=>setCorrectAnswer(cuestionario.id, String.fromCharCode(65 + index)),\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                                    lineNumber: 833,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                            lineNumber: 823,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                                            placeholder: \"Explicaci\\xf3n de esta opci\\xf3n...\",\n                                                                                            value: opcion.explicacion,\n                                                                                            onChange: (e)=>updateOpcion(cuestionario.id, opcion.id, 'explicacion', e.target.value),\n                                                                                            rows: 2,\n                                                                                            className: \"text-sm\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                            lineNumber: 841,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, opcion.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                                    lineNumber: 822,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 820,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            className: \"text-sm font-medium mb-2 block\",\n                                                                            children: \"Explicaci\\xf3n General\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 855,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                                            placeholder: \"Explicaci\\xf3n general del cuestionario...\",\n                                                                            value: cuestionario.explicacion_general,\n                                                                            onChange: (e)=>updateCuestionario(cuestionario.id, 'explicacion_general', e.target.value),\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                            lineNumber: 856,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, cuestionario.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 19\n                                                    }, this)),\n                                                cuestionariosData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        // Convert to JSON and submit\n                                                        const jsonData = JSON.stringify(cuestionariosData, null, 2);\n                                                        setJsonContent(jsonData);\n                                                        handleJsonSubmit();\n                                                    },\n                                                    disabled: isUploading,\n                                                    className: \"w-full bg-green-600 hover:bg-green-700\",\n                                                    children: isUploading ? 'Creando...' : 'Crear Cuestionarios'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                    variant: \"destructive\",\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 888,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, this),\n                                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                    className: \"border-green-200 bg-green-50 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                        className: \"text-green-800\",\n                                        children: \"\\xa1Cuestionarios creados exitosamente!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                lineNumber: 691,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"border-0 shadow-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            className: \"pb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                className: \"text-lg\",\n                                children: [\n                                    \"Cuestionarios (\",\n                                    cuestionarios.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: cuestionarios.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: \"No hay cuestionarios a\\xfan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 17\n                                }, this) : cuestionarios.map((cuestionario)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group border rounded-lg p-3 hover:bg-gray-50 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-sm truncate\",\n                                                            children: cuestionario.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: cuestionario.especialidad\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 923,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        cuestionario.preguntas.length,\n                                                                        \" preguntas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 922,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1 truncate\",\n                                                            children: cuestionario.tema\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 920,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>{\n                                                                if (cuestionario.preguntas.length > 0) {\n                                                                    setSelectedCuestionario(cuestionario.preguntas[0]);\n                                                                    setShowDetailView(true);\n                                                                }\n                                                            },\n                                                            className: \"h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleDelete(cuestionario.id),\n                                                            className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_HelpCircle_Plus_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                            lineNumber: 919,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, cuestionario.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                    lineNumber: 905,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n                lineNumber: 904,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\0. TuResiBo\\\\client\\\\app\\\\upload\\\\cuestionario.tsx\",\n        lineNumber: 689,\n        columnNumber: 5\n    }, this);\n}\n_s(CuestionarioComponent, \"DEzfLj3BJjkqzgGuC+DTDXNpRLw=\");\n_c = CuestionarioComponent;\nvar _c;\n$RefreshReg$(_c, \"CuestionarioComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91cGxvYWQvY3Vlc3Rpb25hcmlvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0k7QUFDRjtBQUNBO0FBQ2tDO0FBQ2xDO0FBQ007QUFDMEI7QUFDZDtBQXNEaEUsc0NBQXNDO0FBQ3RDLFNBQVNtQjtRQUNNQztJQUFiLE1BQU1DLFFBQU9ELHdDQUFBQSx3QkFBb0MsY0FBcENBLDREQUFBQSxzQ0FBc0NJLE9BQU8sQ0FBQyxPQUFPO0lBQ2xFLE9BQU9ILFFBQVE7QUFDakI7QUFFZSxTQUFTSSxzQkFBc0IsS0FBMEM7UUFBMUMsRUFBRUMsVUFBVSxFQUE4QixHQUExQzs7O0lBQzVDLG1CQUFtQjtJQUNuQixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUc1QiwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUM3RSxNQUFNLENBQUM2QixzQkFBc0JDLHdCQUF3QixHQUFHOUIsK0NBQVFBLENBQXNCO0lBQ3RGLE1BQU0sQ0FBQytCLHdCQUF3QkMsMEJBQTBCLEdBQUdoQywrQ0FBUUEsQ0FBZ0I7SUFDcEYsTUFBTSxDQUFDaUMsZ0JBQWdCQyxrQkFBa0IsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ21DLHFCQUFxQkMsdUJBQXVCLEdBQUdwQywrQ0FBUUEsQ0FBZ0I7SUFDOUUsTUFBTSxDQUFDcUMsZUFBZUMsaUJBQWlCLEdBQUd0QywrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUN1QyxhQUFhQyxlQUFlLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN5QyxPQUFPQyxTQUFTLEdBQUcxQywrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDMkMsU0FBU0MsV0FBVyxHQUFHNUMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDNkMsYUFBYUMsZUFBZSxHQUFHOUMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDK0MsaUJBQWlCQyxtQkFBbUIsR0FBR2hELCtDQUFRQSxDQUFjLElBQUlpRDtJQUV4RSw0Q0FBNEM7SUFDNUMsTUFBTUMscUJBQXFCO1FBQ3pCLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sR0FBbUIsT0FBaEJqQyxpQkFBZ0I7WUFDaEQsSUFBSSxDQUFDZ0MsU0FBU0UsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUNsQyxNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFDaENsQixpQkFBaUJpQjtRQUNuQixFQUFFLE9BQU9FLEtBQUs7WUFDWkMsUUFBUWpCLEtBQUssQ0FBQyxpQ0FBaUNnQjtZQUMvQ2YsU0FBUztRQUNYO0lBQ0Y7SUFFQSw4QkFBOEI7SUFDOUJ6QyxnREFBU0E7MkNBQUM7WUFDUmlEO1FBQ0Y7MENBQUcsRUFBRTtJQUVMLDBCQUEwQjtJQUMxQixNQUFNUyxrQkFBa0I7UUFDdEIsTUFBTUMsa0JBQWdDO1lBQ3BDQyxJQUFJQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7WUFDdkJDLFVBQVU7WUFDVkMsb0JBQW9CO1lBQ3BCQyxxQkFBcUI7WUFDckJDLFVBQVU7Z0JBQ1I7b0JBQUVQLElBQUk7b0JBQUtRLFFBQVE7b0JBQU9DLGFBQWE7b0JBQU1DLGFBQWE7Z0JBQUc7Z0JBQzdEO29CQUFFVixJQUFJO29CQUFLUSxRQUFRO29CQUFPQyxhQUFhO29CQUFPQyxhQUFhO2dCQUFHO2dCQUM5RDtvQkFBRVYsSUFBSTtvQkFBS1EsUUFBUTtvQkFBT0MsYUFBYTtvQkFBT0MsYUFBYTtnQkFBRztnQkFDOUQ7b0JBQUVWLElBQUk7b0JBQUtRLFFBQVE7b0JBQU9DLGFBQWE7b0JBQU9DLGFBQWE7Z0JBQUc7YUFDL0Q7UUFDSDtRQUNBM0MscUJBQXFCO2VBQUlEO1lBQW1CaUM7U0FBZ0I7SUFDOUQ7SUFFQSxNQUFNWSxxQkFBcUIsQ0FBQ1g7UUFDMUJqQyxxQkFBcUJELGtCQUFrQjhDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWIsRUFBRSxLQUFLQTtJQUM5RDtJQUVBLE1BQU1jLHFCQUFxQixDQUFDZCxJQUFZZSxPQUEyQkM7UUFDakVqRCxxQkFBcUJELGtCQUFrQm1ELEdBQUcsQ0FBQ0osQ0FBQUEsSUFDekNBLEVBQUViLEVBQUUsS0FBS0EsS0FBSztnQkFBRSxHQUFHYSxDQUFDO2dCQUFFLENBQUNFLE1BQU0sRUFBRUM7WUFBTSxJQUFJSDtJQUU3QztJQUVBLE1BQU1LLGVBQWUsQ0FBQ0MsZ0JBQXdCQyxVQUFrQkwsT0FBeUJDO1FBQ3ZGakQscUJBQXFCRCxrQkFBa0JtRCxHQUFHLENBQUNKLENBQUFBLElBQ3pDQSxFQUFFYixFQUFFLEtBQUttQixpQkFDTDtnQkFDRSxHQUFHTixDQUFDO2dCQUNKTixVQUFVTSxFQUFFTixRQUFRLENBQUNVLEdBQUcsQ0FBQ0ksQ0FBQUEsSUFDdkJBLEVBQUVyQixFQUFFLEtBQUtvQixXQUFXO3dCQUFFLEdBQUdDLENBQUM7d0JBQUUsQ0FBQ04sTUFBTSxFQUFFQztvQkFBTSxJQUFJSztZQUVuRCxJQUNBUjtJQUVSO0lBRUEsTUFBTVMsbUJBQW1CLENBQUNILGdCQUF3Qkk7UUFDaER4RCxxQkFBcUJELGtCQUFrQm1ELEdBQUcsQ0FBQ0osQ0FBQUEsSUFDekNBLEVBQUViLEVBQUUsS0FBS21CLGlCQUNMO2dCQUNFLEdBQUdOLENBQUM7Z0JBQ0pSLG9CQUFvQmtCO2dCQUNwQmhCLFVBQVVNLEVBQUVOLFFBQVEsQ0FBQ1UsR0FBRyxDQUFDSSxDQUFBQSxJQUFNO3dCQUM3QixHQUFHQSxDQUFDO3dCQUNKWixhQUFhWSxFQUFFYixNQUFNLENBQUNnQixVQUFVLENBQUNELG9CQUFvQjtvQkFDdkQ7WUFDRixJQUNBVjtJQUVSO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1ZLHlCQUF5QixPQUFPTixnQkFBd0JPO1FBQzVELElBQUk7WUFDRixNQUFNcEMsV0FBVyxNQUFNQyxNQUFNLEdBQTBENEIsT0FBdkQ3RCxpQkFBZ0IseUNBQXNELE9BQWY2RCxpQkFBa0I7Z0JBQ3ZHUSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ0w7WUFDdkI7WUFFQSxJQUFJLENBQUNwQyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU11QyxzQkFBc0IsTUFBTTFDLFNBQVNLLElBQUk7WUFFL0MscUJBQXFCO1lBQ3JCbEIsaUJBQWlCRCxjQUFjeUMsR0FBRyxDQUFDSixDQUFBQSxJQUNqQ0EsRUFBRWIsRUFBRSxLQUFLbUIsaUJBQWlCYSxzQkFBc0JuQjtZQUdsRCxPQUFPbUI7UUFDVCxFQUFFLE9BQU9wQyxLQUFLO1lBQ1pDLFFBQVFqQixLQUFLLENBQUMsZ0NBQWdDZ0I7WUFDOUNmLFNBQVM7WUFDVCxNQUFNZTtRQUNSO0lBQ0Y7SUFFQSxNQUFNcUMsMkJBQTJCLE9BQU9kO1FBQ3RDLElBQUk7WUFDRixNQUFNN0IsV0FBVyxNQUFNQyxNQUFNLEdBQTBENEIsT0FBdkQ3RCxpQkFBZ0IseUNBQXNELE9BQWY2RCxpQkFBa0I7Z0JBQ3ZHUSxRQUFRO1lBQ1Y7WUFFQSxJQUFJLENBQUNyQyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLHFCQUFxQjtZQUNyQmhCLGlCQUFpQkQsY0FBY29DLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWIsRUFBRSxLQUFLbUI7WUFFcEQsSUFBSW5ELHdCQUF3QkEscUJBQXFCZ0MsRUFBRSxLQUFLbUIsZUFBZWhCLFFBQVEsSUFBSTtnQkFDakZsQyx3QkFBd0I7Z0JBQ3hCSSxrQkFBa0I7WUFDcEI7UUFDRixFQUFFLE9BQU91QixLQUFLO1lBQ1pDLFFBQVFqQixLQUFLLENBQUMsZ0NBQWdDZ0I7WUFDOUNmLFNBQVM7UUFDWDtJQUNGO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1xRCxjQUFjLE9BQU9DLE1BQVlDLE1BQTZCakIsZ0JBQXdCQztRQUMxRixNQUFNaUIsV0FBVyxHQUFxQkQsT0FBbEJqQixnQkFBZSxLQUFXQyxPQUFSZ0IsTUFBSyxLQUEwQixPQUF2QmhCLFlBQVk7UUFDMURqQyxtQkFBbUJtRCxDQUFBQSxPQUFRLElBQUlsRCxJQUFJO21CQUFJa0Q7Z0JBQU1EO2FBQVM7UUFFdEQsSUFBSTtZQUNGLE1BQU1FLFdBQVcsSUFBSUM7WUFDckJELFNBQVNFLE1BQU0sQ0FBQyxRQUFRTjtZQUN4QkksU0FBU0UsTUFBTSxDQUFDLGdCQUFnQjtZQUNoQ0YsU0FBU0UsTUFBTSxDQUFDLGNBQWN0QixlQUFlaEIsUUFBUTtZQUVyRCxNQUFNYixXQUFXLE1BQU1DLE1BQU0sR0FBbUIsT0FBaEJqQyxpQkFBZ0Isd0NBQXNDO2dCQUNwRnFFLFFBQVE7Z0JBQ1JFLE1BQU1VO1lBQ1I7WUFFQSxJQUFJLENBQUNqRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU1pRCxTQUFTLE1BQU1wRCxTQUFTSyxJQUFJO1lBRWxDLGlEQUFpRDtZQUNqRCxNQUFNZ0QsZUFBZW5FLGNBQWNvRSxJQUFJLENBQUMvQixDQUFBQSxJQUFLQSxFQUFFYixFQUFFLEtBQUttQjtZQUN0RCxJQUFJd0IsZ0JBQWdCQSxhQUFhRSxTQUFTLENBQUNDLE1BQU0sR0FBRyxHQUFHO2dCQUNyRCxNQUFNMUMsV0FBV3VDLGFBQWFFLFNBQVMsQ0FBQyxFQUFFO2dCQUUxQyxJQUFJVCxTQUFTLFlBQVk7b0JBQ3ZCaEMsU0FBUzJDLFVBQVUsR0FBR0wsT0FBT00sR0FBRztvQkFDaEM1QyxTQUFTNkMsV0FBVyxHQUFHUCxPQUFPUSxJQUFJO2dCQUNwQyxPQUFPLElBQUlkLFNBQVMsWUFBWWhCLFVBQVU7b0JBQ3hDLE1BQU1aLFNBQVNKLFNBQVNHLFFBQVEsQ0FBQ3FDLElBQUksQ0FBQ3ZCLENBQUFBLElBQUtBLEVBQUVyQixFQUFFLEtBQUtvQjtvQkFDcEQsSUFBSVosUUFBUTt3QkFDVkEsT0FBT3VDLFVBQVUsR0FBR0wsT0FBT00sR0FBRzt3QkFDOUJ4QyxPQUFPeUMsV0FBVyxHQUFHUCxPQUFPUSxJQUFJO29CQUNsQztnQkFDRjtnQkFFQSxxQkFBcUI7Z0JBQ3JCLE1BQU16Qix1QkFBdUJOLGdCQUFnQjtvQkFBRTBCLFdBQVdGLGFBQWFFLFNBQVM7Z0JBQUM7WUFDbkY7WUFFQSxPQUFPSDtRQUNULEVBQUUsT0FBTzlDLEtBQUs7WUFDWkMsUUFBUWpCLEtBQUssQ0FBQywwQkFBMEJnQjtZQUN4Q2YsU0FBUztZQUNULE1BQU1lO1FBQ1IsU0FBVTtZQUNSVCxtQkFBbUJtRCxDQUFBQTtnQkFDakIsTUFBTWEsU0FBUyxJQUFJL0QsSUFBSWtEO2dCQUN2QmEsT0FBT0MsTUFBTSxDQUFDZjtnQkFDZCxPQUFPYztZQUNUO1FBQ0Y7SUFDRjtJQUVBLE1BQU1FLGNBQWMsT0FBT2xDLGdCQUF3QmlCLE1BQTZCaEI7UUFDOUUsSUFBSTtZQUNGLE1BQU11QixlQUFlbkUsY0FBY29FLElBQUksQ0FBQy9CLENBQUFBLElBQUtBLEVBQUViLEVBQUUsS0FBS21CO1lBQ3RELElBQUl3QixnQkFBZ0JBLGFBQWFFLFNBQVMsQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7Z0JBQ3JELE1BQU0xQyxXQUFXdUMsYUFBYUUsU0FBUyxDQUFDLEVBQUU7Z0JBRTFDLElBQUlULFNBQVMsWUFBWTtvQkFDdkJoQyxTQUFTMkMsVUFBVSxHQUFHTztvQkFDdEJsRCxTQUFTNkMsV0FBVyxHQUFHSztnQkFDekIsT0FBTyxJQUFJbEIsU0FBUyxZQUFZaEIsVUFBVTtvQkFDeEMsTUFBTVosU0FBU0osU0FBU0csUUFBUSxDQUFDcUMsSUFBSSxDQUFDdkIsQ0FBQUEsSUFBS0EsRUFBRXJCLEVBQUUsS0FBS29CO29CQUNwRCxJQUFJWixRQUFRO3dCQUNWQSxPQUFPdUMsVUFBVSxHQUFHTzt3QkFDcEI5QyxPQUFPeUMsV0FBVyxHQUFHSztvQkFDdkI7Z0JBQ0Y7Z0JBRUEscUJBQXFCO2dCQUNyQixNQUFNN0IsdUJBQXVCTixnQkFBZ0I7b0JBQUUwQixXQUFXRixhQUFhRSxTQUFTO2dCQUFDO1lBQ25GO1FBQ0YsRUFBRSxPQUFPakQsS0FBSztZQUNaQyxRQUFRakIsS0FBSyxDQUFDLHlCQUF5QmdCO1lBQ3ZDZixTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU0wRSxlQUFlLE9BQU9wQztRQUMxQixJQUFJLENBQUNxQyxRQUFRLDZEQUE2RDtRQUUxRSxJQUFJO1lBQ0YsTUFBTWxFLFdBQVcsTUFBTUMsTUFBTSxHQUEwRDRCLE9BQXZEN0QsaUJBQWdCLHlDQUFzRCxPQUFmNkQsaUJBQWtCO2dCQUN2R1EsUUFBUTtZQUNWO1lBRUEsSUFBSSxDQUFDckMsU0FBU0UsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUVsQ2hCLGlCQUFpQkQsY0FBY29DLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRWIsRUFBRSxLQUFLbUI7WUFDcERwQyxXQUFXO1lBQ1gwRSxXQUFXLElBQU0xRSxXQUFXLFFBQVE7UUFDdEMsRUFBRSxPQUFPYSxLQUFLO1lBQ1pDLFFBQVFqQixLQUFLLENBQUMsZ0NBQWdDZ0I7WUFDOUNmLFNBQVM7UUFDWDtJQUNGO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU02RSxtQkFBbUI7UUFDdkIsSUFBSSxDQUFDMUUsWUFBWTJFLElBQUksSUFBSTtZQUN2QjlFLFNBQVM7WUFDVDtRQUNGO1FBRUEsSUFBSSxDQUFDaEIsV0FBVytGLFlBQVksSUFBSSxDQUFDL0YsV0FBV2dHLElBQUksRUFBRTtZQUNoRGhGLFNBQVM7WUFDVDtRQUNGO1FBRUFGLGVBQWU7UUFDZkUsU0FBUztRQUNURSxXQUFXO1FBRVgsSUFBSTtZQUNGLDZCQUE2QjtZQUM3QixJQUFJK0U7WUFDSixJQUFJO2dCQUNGQSxhQUFhaEMsS0FBS2lDLEtBQUssQ0FBQy9FO1lBQzFCLEVBQUUsT0FBT2dGLFlBQVk7Z0JBQ25CLE1BQU0sSUFBSXZFLE1BQU07WUFDbEI7WUFFQSxxQkFBcUI7WUFDckIsSUFBSSxDQUFDd0UsTUFBTUMsT0FBTyxDQUFDSixlQUFlQSxXQUFXaEIsTUFBTSxLQUFLLEdBQUc7Z0JBQ3pELE1BQU0sSUFBSXJELE1BQU07WUFDbEI7WUFFQSwyQkFBMkI7WUFDM0IsTUFBTTBFLGlCQUFpQjtnQkFBQztnQkFBTTtnQkFBWTtnQkFBWTtnQkFBc0I7YUFBc0I7WUFDbEcsS0FBSyxNQUFNeEIsZ0JBQWdCbUIsV0FBWTtnQkFDckMsS0FBSyxNQUFNL0MsU0FBU29ELGVBQWdCO29CQUNsQyxJQUFJLENBQUVwRCxDQUFBQSxTQUFTNEIsWUFBVyxHQUFJO3dCQUM1QixNQUFNLElBQUlsRCxNQUFNLDZCQUFtQyxPQUFOc0I7b0JBQy9DO2dCQUNGO1lBQ0Y7WUFFQSxNQUFNd0IsV0FBVyxJQUFJQztZQUNyQkQsU0FBU0UsTUFBTSxDQUFDLGdCQUFnQnpEO1lBQ2hDdUQsU0FBU0UsTUFBTSxDQUFDLGdCQUFnQjVFLFdBQVcrRixZQUFZO1lBQ3ZEckIsU0FBU0UsTUFBTSxDQUFDLFdBQVc7WUFDM0JGLFNBQVNFLE1BQU0sQ0FBQyxRQUFRNUUsV0FBV2dHLElBQUk7WUFDdkMsSUFBSWhHLFdBQVd1RyxNQUFNLEVBQUU7Z0JBQ3JCN0IsU0FBU0UsTUFBTSxDQUFDLFVBQVU1RSxXQUFXdUcsTUFBTTtZQUM3QztZQUVBLE1BQU05RSxXQUFXLE1BQU1DLE1BQU0sR0FBbUIsT0FBaEJqQyxpQkFBZ0IsOENBQTRDO2dCQUMxRnFFLFFBQVE7Z0JBQ1JFLE1BQU1VO1lBQ1I7WUFFQSxJQUFJLENBQUNqRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU02RSxZQUFZLE1BQU0vRSxTQUFTSyxJQUFJO2dCQUNyQyxNQUFNLElBQUlGLE1BQU00RSxVQUFVQyxNQUFNLElBQUk7WUFDdEM7WUFFQSxNQUFNNUIsU0FBUyxNQUFNcEQsU0FBU0ssSUFBSTtZQUVsQyxJQUFJK0MsT0FBTzVELE9BQU8sRUFBRTtnQkFDbEJDLFdBQVc7Z0JBQ1hGLFNBQVM7Z0JBQ1RJLGVBQWU7Z0JBQ2ZJO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUlJLE1BQU1pRCxPQUFPNkIsT0FBTyxJQUFJO1lBQ3BDO1FBQ0YsRUFBRSxPQUFPM0UsS0FBSztZQUNaQyxRQUFRakIsS0FBSyxDQUFDLDBCQUEwQmdCO1lBQ3hDZixTQUFTZSxlQUFlSCxRQUFRRyxJQUFJMkUsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUjVGLGVBQWU7UUFDakI7SUFDRjtJQUVBLHVEQUF1RDtJQUN2RCxNQUFNNkYsZUFBZTtZQUFDLEVBQUU3QixZQUFZLEVBQUV4QixjQUFjLEVBQUVzRCxTQUFTLEVBQUVDLFlBQVksRUFLNUU7O1FBQ0MsTUFBTSxDQUFDQyxtQkFBbUJDLHFCQUFxQixHQUFHekksK0NBQVFBLENBQWV3RztRQUN6RSxNQUFNLENBQUNrQyxVQUFVQyxZQUFZLEdBQUczSSwrQ0FBUUEsQ0FBQztRQUV6QyxNQUFNNEksYUFBYTtZQUNqQkQsWUFBWTtZQUNaLElBQUk7Z0JBQ0YsTUFBTXJELHVCQUF1Qk4sZ0JBQWdCO29CQUFFMEIsV0FBVzt3QkFBQzhCO3FCQUFrQjtnQkFBQztnQkFDOUVEO1lBQ0YsRUFBRSxPQUFPOUUsS0FBSztnQkFDWkMsUUFBUWpCLEtBQUssQ0FBQyxpQkFBaUJnQjtZQUNqQyxTQUFVO2dCQUNSa0YsWUFBWTtZQUNkO1FBQ0Y7UUFFQSxNQUFNRSxvQkFBb0IsT0FBTzdDLE1BQVlDLE1BQTZCaEI7WUFDeEUsSUFBSTtnQkFDRixNQUFNYyxZQUFZQyxNQUFNQyxNQUFNakIsZ0JBQWdCQztnQkFDOUMsZ0NBQWdDO2dCQUNoQy9CO1lBQ0YsRUFBRSxPQUFPTyxLQUFLO2dCQUNaQyxRQUFRakIsS0FBSyxDQUFDLDBCQUEwQmdCO1lBQzFDO1FBQ0Y7UUFFQSxNQUFNcUYsb0JBQW9CLE9BQU83QyxNQUE2QmhCO1lBQzVELElBQUk7Z0JBQ0YsTUFBTWlDLFlBQVlsQyxnQkFBZ0JpQixNQUFNaEI7Z0JBQ3hDLGdDQUFnQztnQkFDaEMvQjtZQUNGLEVBQUUsT0FBT08sS0FBSztnQkFDWkMsUUFBUWpCLEtBQUssQ0FBQyx5QkFBeUJnQjtZQUN6QztRQUNGO1FBRUEscUJBQ0UsOERBQUNzRjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FBa0M7Ozs7OztzQ0FDaEQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaViwwQkFDQzs7a0RBQ0UsOERBQUNwSSx5REFBTUE7d0NBQ0xnSixTQUFTTjt3Q0FDVE8sVUFBVVQ7d0NBQ1ZNLFdBQVU7a0RBRVROLFdBQVcsaUJBQWlCOzs7Ozs7a0RBRS9CLDhEQUFDeEkseURBQU1BO3dDQUNMZ0osU0FBU1g7d0NBQ1RhLFNBQVE7a0RBQ1Q7Ozs7Ozs7NkRBS0gsOERBQUNsSix5REFBTUE7Z0NBQ0xnSixTQUFTWDtnQ0FDVGEsU0FBUTswQ0FDVDs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUVAsOERBQUNMO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0s7NEJBQUdMLFdBQVU7c0NBQTJDOzs7Ozs7d0JBQ3hEViwwQkFDQyw4REFBQzVILDZEQUFRQTs0QkFDUG1FLE9BQU8yRCxrQkFBa0J2RSxRQUFROzRCQUNqQ3FGLFVBQVUsQ0FBQ0MsSUFBTWQscUJBQXFCO29DQUFDLEdBQUdELGlCQUFpQjtvQ0FBRXZFLFVBQVVzRixFQUFFQyxNQUFNLENBQUMzRSxLQUFLO2dDQUFBOzRCQUNyRjRFLE1BQU07NEJBQ05ULFdBQVU7Ozs7O2lEQUdaLDhEQUFDVTs0QkFBRVYsV0FBVTtzQ0FBc0N4QyxhQUFhdkMsUUFBUTs7Ozs7O3NDQUkxRSw4REFBQzhFOzRCQUFJQyxXQUFVO3NDQUNaeEMsYUFBYUksVUFBVSxpQkFDdEIsOERBQUNtQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNXO3dDQUNDQyxLQUFLcEQsYUFBYUksVUFBVTt3Q0FDNUJpRCxLQUFJO3dDQUNKYixXQUFVOzs7Ozs7b0NBRVhWLDJCQUNDLDhEQUFDcEkseURBQU1BO3dDQUNMZ0osU0FBUyxJQUFNSixrQkFBa0I7d0NBQ2pDTSxTQUFRO3dDQUNSVSxNQUFLO3dDQUNMZCxXQUFVO2tEQUVWLDRFQUFDakksK0dBQUNBOzRDQUFDaUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0FJakJWLDJCQUNGLDhEQUFDUzs7a0RBQ0MsOERBQUMzSSx1REFBS0E7d0NBQUM0SSxXQUFVO2tEQUFzQjs7Ozs7O2tEQUN2Qyw4REFBQzdJLHVEQUFLQTt3Q0FDSjhGLE1BQUs7d0NBQ0w4RCxRQUFPO3dDQUNQVCxVQUFVLENBQUNDO2dEQUNJQTs0Q0FBYixNQUFNdkQsUUFBT3VELGtCQUFBQSxFQUFFQyxNQUFNLENBQUNRLEtBQUssY0FBZFQsc0NBQUFBLGVBQWdCLENBQUMsRUFBRTs0Q0FDaEMsSUFBSXZELE1BQU02QyxrQkFBa0I3QyxNQUFNO3dDQUNwQzt3Q0FDQWdELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9wQiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFBR0wsV0FBVTtzQ0FBc0M7Ozs7Ozt3QkFDbERWLENBQUFBLFlBQVlFLGtCQUFrQnBFLFFBQVEsR0FBR29DLGFBQWFwQyxRQUFRLEVBQUVVLEdBQUcsQ0FBQyxDQUFDVCxRQUFRNEYsc0JBQzdFLDhEQUFDbEI7Z0NBRUNDLFdBQVcseUJBSVYsT0FIQzNFLE9BQU9DLFdBQVcsR0FDZCxpQ0FDQTswQ0FHTiw0RUFBQ3lFO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVcsMkZBSWYsT0FIQzNFLE9BQU9DLFdBQVcsR0FDZCw0QkFDQTtzREFFSDRGLE9BQU9DLFlBQVksQ0FBQyxLQUFLRjs7Ozs7O3NEQUU1Qiw4REFBQ2xCOzRDQUFJQyxXQUFVOztnREFFWlYsMEJBQ0MsOERBQUNuSSx1REFBS0E7b0RBQ0owRSxPQUFPUixPQUFPQSxNQUFNO29EQUNwQmlGLFVBQVUsQ0FBQ0M7d0RBQ1QsTUFBTWEsa0JBQWtCNUIsa0JBQWtCcEUsUUFBUSxDQUFDVSxHQUFHLENBQUNJLENBQUFBLElBQ3JEQSxFQUFFckIsRUFBRSxLQUFLUSxPQUFPUixFQUFFLEdBQUc7Z0VBQUMsR0FBR3FCLENBQUM7Z0VBQUViLFFBQVFrRixFQUFFQyxNQUFNLENBQUMzRSxLQUFLOzREQUFBLElBQUlLO3dEQUV4RHVELHFCQUFxQjs0REFBQyxHQUFHRCxpQkFBaUI7NERBQUVwRSxVQUFVZ0c7d0RBQWU7b0RBQ3ZFO29EQUNBcEIsV0FBVTs7Ozs7eUVBR1osOERBQUNVO29EQUFFVixXQUFVOzhEQUE2QjNFLE9BQU9BLE1BQU07Ozs7Ozs4REFJekQsOERBQUMwRTtvREFBSUMsV0FBVTs4REFDWlYsMEJBQ0MsOERBQUM1SCw2REFBUUE7d0RBQ1BtRSxPQUFPUixPQUFPRSxXQUFXO3dEQUN6QitFLFVBQVUsQ0FBQ0M7NERBQ1QsTUFBTWEsa0JBQWtCNUIsa0JBQWtCcEUsUUFBUSxDQUFDVSxHQUFHLENBQUNJLENBQUFBLElBQ3JEQSxFQUFFckIsRUFBRSxLQUFLUSxPQUFPUixFQUFFLEdBQUc7b0VBQUMsR0FBR3FCLENBQUM7b0VBQUVYLGFBQWFnRixFQUFFQyxNQUFNLENBQUMzRSxLQUFLO2dFQUFBLElBQUlLOzREQUU3RHVELHFCQUFxQjtnRUFBQyxHQUFHRCxpQkFBaUI7Z0VBQUVwRSxVQUFVZ0c7NERBQWU7d0RBQ3ZFO3dEQUNBWCxNQUFNO3dEQUNOVCxXQUFVOzs7Ozs2RUFHWiw4REFBQ1U7d0RBQUVWLFdBQVU7OzBFQUNYLDhEQUFDcUI7MEVBQU87Ozs7Ozs0REFBcUI7NERBQUVoRyxPQUFPRSxXQUFXOzs7Ozs7Ozs7Ozs7OERBTXZELDhEQUFDd0U7b0RBQUlDLFdBQVU7OERBQ1ozRSxPQUFPdUMsVUFBVSxpQkFDaEIsOERBQUNtQzt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNXO2dFQUNDQyxLQUFLdkYsT0FBT3VDLFVBQVU7Z0VBQ3RCaUQsS0FBSyxvQkFBaUQsT0FBaENLLE9BQU9DLFlBQVksQ0FBQyxLQUFLRjtnRUFDL0NqQixXQUFVOzs7Ozs7NERBRVhWLDJCQUNDLDhEQUFDcEkseURBQU1BO2dFQUNMZ0osU0FBUyxJQUFNSixrQkFBa0IsVUFBVXpFLE9BQU9SLEVBQUU7Z0VBQ3BEdUYsU0FBUTtnRUFDUlUsTUFBSztnRUFDTGQsV0FBVTswRUFFViw0RUFBQ2pJLCtHQUFDQTtvRUFBQ2lJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7K0RBSWpCViwyQkFDRiw4REFBQ1M7OzBFQUNDLDhEQUFDM0ksdURBQUtBO2dFQUFDNEksV0FBVTswRUFBc0I7Ozs7OzswRUFDdkMsOERBQUM3SSx1REFBS0E7Z0VBQ0o4RixNQUFLO2dFQUNMOEQsUUFBTztnRUFDUFQsVUFBVSxDQUFDQzt3RUFDSUE7b0VBQWIsTUFBTXZELFFBQU91RCxrQkFBQUEsRUFBRUMsTUFBTSxDQUFDUSxLQUFLLGNBQWRULHNDQUFBQSxlQUFnQixDQUFDLEVBQUU7b0VBQ2hDLElBQUl2RCxNQUFNNkMsa0JBQWtCN0MsTUFBTSxVQUFVM0IsT0FBT1IsRUFBRTtnRUFDdkQ7Z0VBQ0FtRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztnREFPakJWLDJCQUNDLDhEQUFDUztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNzQjs0REFDQ3JFLE1BQUs7NERBQ0xzRSxNQUFNLGtCQUFpQyxPQUFmdkY7NERBQ3hCd0YsU0FBU25HLE9BQU9DLFdBQVc7NERBQzNCZ0YsVUFBVTtnRUFDUixNQUFNbUIsU0FBU1AsT0FBT0MsWUFBWSxDQUFDLEtBQUtGO2dFQUN4QyxNQUFNRyxrQkFBa0I1QixrQkFBa0JwRSxRQUFRLENBQUNVLEdBQUcsQ0FBQ0ksQ0FBQUEsSUFBTTt3RUFDM0QsR0FBR0EsQ0FBQzt3RUFDSlosYUFBYVksRUFBRXJCLEVBQUUsS0FBS1EsT0FBT1IsRUFBRTtvRUFDakM7Z0VBQ0E0RSxxQkFBcUI7b0VBQ25CLEdBQUdELGlCQUFpQjtvRUFDcEJwRSxVQUFVZ0c7b0VBQ1ZsRyxvQkFBb0J1RztnRUFDdEI7NERBQ0Y7NERBQ0F6QixXQUFVOzs7Ozs7c0VBRVosOERBQUM1SSx1REFBS0E7NERBQUM0SSxXQUFVO3NFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBOUc5QjNFLE9BQU9SLEVBQUU7Ozs7Ozs7Ozs7OzhCQXdIcEIsOERBQUNrRjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUMwQjs0QkFBRzFCLFdBQVU7c0NBQW9DOzs7Ozs7c0NBQ2xELDhEQUFDVTs0QkFBRVYsV0FBVTs7Z0NBQWlCO2dDQUFReEMsYUFBYXRDLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs4QkFJdkUsOERBQUM2RTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFHTCxXQUFVO3NDQUE2Qzs7Ozs7O3dCQUMxRFYsMEJBQ0MsOERBQUM1SCw2REFBUUE7NEJBQ1BtRSxPQUFPMkQsa0JBQWtCckUsbUJBQW1COzRCQUM1Q21GLFVBQVUsQ0FBQ0MsSUFBTWQscUJBQXFCO29DQUFDLEdBQUdELGlCQUFpQjtvQ0FBRXJFLHFCQUFxQm9GLEVBQUVDLE1BQU0sQ0FBQzNFLEtBQUs7Z0NBQUE7NEJBQ2hHNEUsTUFBTTs7Ozs7aURBR1IsOERBQUNDOzRCQUFFVixXQUFVO3NDQUFpQ3hDLGFBQWFyQyxtQkFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUt4RjtRQTdRTWtFO0lBK1FOLHdCQUF3QjtJQUN4QixJQUFJcEcsa0JBQWtCSixzQkFBc0I7UUFDMUMscUJBQ0UsOERBQUNrSDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQzlJLHlEQUFNQTtvQkFDTGdKLFNBQVMsSUFBTWhILGtCQUFrQjtvQkFDakNrSCxTQUFRO29CQUNSSixXQUFVOzhCQUNYOzs7Ozs7OEJBR0QsOERBQUNYO29CQUNDN0IsY0FBYzNFO29CQUNkbUQsZ0JBQWdCMkYsU0FBUzlJLHFCQUFxQmdDLEVBQUU7b0JBQ2hEeUUsV0FBV25HLHdCQUF3QndJLFNBQVM5SSxxQkFBcUJnQyxFQUFFO29CQUNuRTBFLGNBQWM7d0JBQ1osSUFBSXBHLHdCQUF3QndJLFNBQVM5SSxxQkFBcUJnQyxFQUFFLEdBQUc7NEJBQzdEekIsdUJBQXVCO3dCQUN6QixPQUFPOzRCQUNMQSx1QkFBdUJ1SSxTQUFTOUkscUJBQXFCZ0MsRUFBRTt3QkFDekQ7b0JBQ0Y7Ozs7Ozs7Ozs7OztJQUlSO0lBRUEscUJBQ0UsOERBQUNrRjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUMzSSxxREFBSUE7b0JBQUMySSxXQUFVOztzQ0FDZCw4REFBQ3pJLDJEQUFVQTs0QkFBQ3lJLFdBQVU7c0NBQ3BCLDRFQUFDeEksMERBQVNBO2dDQUFDd0ksV0FBVTs7a0RBQ25CLDhEQUFDbEksZ0hBQUlBO3dDQUFDa0ksV0FBVTs7Ozs7O29DQUFZOzs7Ozs7Ozs7Ozs7c0NBSWhDLDhEQUFDMUksNERBQVdBOzs4Q0FFViw4REFBQ3lJO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDckksZ0hBQU1BO29EQUFDcUksV0FBVTs7Ozs7OzhEQUNsQiw4REFBQ0s7b0RBQUdMLFdBQVU7OERBQTRCOzs7Ozs7Ozs7Ozs7c0RBRTVDLDhEQUFDVTs0Q0FBRVYsV0FBVTtzREFBNkI7Ozs7OztzREFHMUMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3RJLDZEQUFRQTtvREFDUG1ELElBQUc7b0RBQ0grRyxhQUFZO29EQUNaL0YsT0FBT2hDO29EQUNQeUcsVUFBVSxDQUFDQyxJQUFNekcsZUFBZXlHLEVBQUVDLE1BQU0sQ0FBQzNFLEtBQUs7b0RBQzlDNEUsTUFBTTtvREFDTlQsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDOUkseURBQU1BO29EQUNMK0YsTUFBSztvREFDTGlELFNBQVMzQjtvREFDVDRCLFVBQVUsQ0FBQ3RHLFlBQVkyRSxJQUFJLE1BQU1qRjtvREFDakN5RyxXQUFVOztzRUFFViw4REFBQ3JJLGdIQUFNQTs0REFBQ3FJLFdBQVU7Ozs7Ozt3REFDakJ6RyxjQUFjLGtCQUFrQjs7Ozs7Ozs7Ozs7OztzREFLckMsOERBQUNzSTs0Q0FBUTdCLFdBQVU7OzhEQUNqQiw4REFBQzhCO29EQUFROUIsV0FBVTs4REFBMkQ7Ozs7Ozs4REFHOUUsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDK0I7a0VBQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQXdCYiw4REFBQ2hDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNnQztnREFBS2hDLFdBQVU7Ozs7Ozs7Ozs7O3NEQUVsQiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNnQztnREFBS2hDLFdBQVU7MERBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLbEQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDcEksZ0hBQVVBO29EQUFDb0ksV0FBVTs7Ozs7OzhEQUN0Qiw4REFBQ0s7b0RBQUdMLFdBQVU7OERBQTZCOzs7Ozs7Ozs7Ozs7c0RBRzdDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzVJLHVEQUFLQTs0REFBQzRJLFdBQVU7c0VBQXNCOzs7Ozs7c0VBQ3ZDLDhEQUFDOUkseURBQU1BOzREQUNMK0YsTUFBSzs0REFDTG1ELFNBQVE7NERBQ1JVLE1BQUs7NERBQ0xaLFNBQVN2Rjs0REFDVHFGLFdBQVU7OzhFQUVWLDhEQUFDbEksZ0hBQUlBO29FQUFDa0ksV0FBVTs7Ozs7O2dFQUFZOzs7Ozs7Ozs7Ozs7O2dEQUsvQnJILGtCQUFrQm1ELEdBQUcsQ0FBQyxDQUFDMEIsNkJBQ3RCLDhEQUFDbkcscURBQUlBO3dEQUF1QjJJLFdBQVU7a0VBQ3BDLDRFQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQzVJLHVEQUFLQTs0RUFBQzRJLFdBQVU7O2dGQUFzQjtnRkFBY3hDLGFBQWEzQyxFQUFFOzs7Ozs7O3NGQUNwRSw4REFBQzNELHlEQUFNQTs0RUFDTCtGLE1BQUs7NEVBQ0xtRCxTQUFROzRFQUNSVSxNQUFLOzRFQUNMWixTQUFTLElBQU0xRSxtQkFBbUJnQyxhQUFhM0MsRUFBRTs0RUFDakRtRixXQUFVO3NGQUVWLDRFQUFDakksK0dBQUNBO2dGQUFDaUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBS2pCLDhEQUFDRDs7c0ZBQ0MsOERBQUMzSSx1REFBS0E7NEVBQUM0SSxXQUFVO3NGQUFpQzs7Ozs7O3NGQUNsRCw4REFBQ3RJLDZEQUFRQTs0RUFDUGtLLGFBQVk7NEVBQ1ovRixPQUFPMkIsYUFBYXZDLFFBQVE7NEVBQzVCcUYsVUFBVSxDQUFDQyxJQUFNNUUsbUJBQW1CNkIsYUFBYTNDLEVBQUUsRUFBRSxZQUFZMEYsRUFBRUMsTUFBTSxDQUFDM0UsS0FBSzs0RUFDL0U0RSxNQUFNOzs7Ozs7Ozs7Ozs7OEVBS1YsOERBQUNWOztzRkFDQyw4REFBQzNJLHVEQUFLQTs0RUFBQzRJLFdBQVU7c0ZBQWlDOzs7Ozs7c0ZBQ2xELDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDWnhDLGFBQWFwQyxRQUFRLENBQUNVLEdBQUcsQ0FBQyxDQUFDVCxRQUFRNEYsc0JBQ2xDLDhEQUFDbEI7b0ZBQW9CQyxXQUFVOztzR0FDN0IsOERBQUNEOzRGQUFJQyxXQUFVOzs4R0FDYiw4REFBQ2dDO29HQUFLaEMsV0FBVTs7d0dBQ2JrQixPQUFPQyxZQUFZLENBQUMsS0FBS0Y7d0dBQU87Ozs7Ozs7OEdBRW5DLDhEQUFDOUosdURBQUtBO29HQUNKeUssYUFBYSxhQUEwQyxPQUFoQ1YsT0FBT0MsWUFBWSxDQUFDLEtBQUtGO29HQUNoRHBGLE9BQU9SLE9BQU9BLE1BQU07b0dBQ3BCaUYsVUFBVSxDQUFDQyxJQUFNeEUsYUFBYXlCLGFBQWEzQyxFQUFFLEVBQUVRLE9BQU9SLEVBQUUsRUFBRSxVQUFVMEYsRUFBRUMsTUFBTSxDQUFDM0UsS0FBSztvR0FDbEZtRSxXQUFVOzs7Ozs7OEdBRVosOERBQUNzQjtvR0FDQ3JFLE1BQUs7b0dBQ0xzRSxNQUFNLFdBQTJCLE9BQWhCL0QsYUFBYTNDLEVBQUU7b0dBQ2hDMkcsU0FBU25HLE9BQU9DLFdBQVc7b0dBQzNCZ0YsVUFBVSxJQUFNbkUsaUJBQWlCcUIsYUFBYTNDLEVBQUUsRUFBRXFHLE9BQU9DLFlBQVksQ0FBQyxLQUFLRjtvR0FDM0VqQixXQUFVOzs7Ozs7Ozs7Ozs7c0dBR2QsOERBQUN0SSw2REFBUUE7NEZBQ1BrSyxhQUFZOzRGQUNaL0YsT0FBT1IsT0FBT0UsV0FBVzs0RkFDekIrRSxVQUFVLENBQUNDLElBQU14RSxhQUFheUIsYUFBYTNDLEVBQUUsRUFBRVEsT0FBT1IsRUFBRSxFQUFFLGVBQWUwRixFQUFFQyxNQUFNLENBQUMzRSxLQUFLOzRGQUN2RjRFLE1BQU07NEZBQ05ULFdBQVU7Ozs7Ozs7bUZBeEJKM0UsT0FBT1IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFnQ3pCLDhEQUFDa0Y7O3NGQUNDLDhEQUFDM0ksdURBQUtBOzRFQUFDNEksV0FBVTtzRkFBaUM7Ozs7OztzRkFDbEQsOERBQUN0SSw2REFBUUE7NEVBQ1BrSyxhQUFZOzRFQUNaL0YsT0FBTzJCLGFBQWFyQyxtQkFBbUI7NEVBQ3ZDbUYsVUFBVSxDQUFDQyxJQUFNNUUsbUJBQW1CNkIsYUFBYTNDLEVBQUUsRUFBRSx1QkFBdUIwRixFQUFFQyxNQUFNLENBQUMzRSxLQUFLOzRFQUMxRjRFLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1REFyRUhqRCxhQUFhM0MsRUFBRTs7Ozs7Z0RBNEUzQmxDLGtCQUFrQmdGLE1BQU0sR0FBRyxtQkFDMUIsOERBQUN6Ryx5REFBTUE7b0RBQ0wrRixNQUFLO29EQUNMaUQsU0FBUzt3REFDUCw2QkFBNkI7d0RBQzdCLE1BQU0rQixXQUFXdEYsS0FBS0MsU0FBUyxDQUFDakUsbUJBQW1CLE1BQU07d0RBQ3pEbUIsZUFBZW1JO3dEQUNmMUQ7b0RBQ0Y7b0RBQ0E0QixVQUFVNUc7b0RBQ1Z5RyxXQUFVOzhEQUVUekcsY0FBYyxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBT3JDRSx1QkFDQyw4REFBQ3hCLHVEQUFLQTtvQ0FBQ21JLFNBQVE7b0NBQWNKLFdBQVU7OENBQ3JDLDRFQUFDOUgsa0VBQWdCQTtrREFBRXVCOzs7Ozs7Ozs7OztnQ0FJdEJFLHlCQUNDLDhEQUFDMUIsdURBQUtBO29DQUFDK0gsV0FBVTs4Q0FDZiw0RUFBQzlILGtFQUFnQkE7d0NBQUM4SCxXQUFVO2tEQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVdkQsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDM0kscURBQUlBO29CQUFDMkksV0FBVTs7c0NBQ2QsOERBQUN6SSwyREFBVUE7NEJBQUN5SSxXQUFVO3NDQUNwQiw0RUFBQ3hJLDBEQUFTQTtnQ0FBQ3dJLFdBQVU7O29DQUFVO29DQUFnQjNHLGNBQWNzRSxNQUFNO29DQUFDOzs7Ozs7Ozs7Ozs7c0NBRXRFLDhEQUFDckcsNERBQVdBO3NDQUNWLDRFQUFDeUk7Z0NBQUlDLFdBQVU7MENBQ1ozRyxjQUFjc0UsTUFBTSxLQUFLLGtCQUN4Qiw4REFBQ29DO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BJLGdIQUFVQTs0Q0FBQ29JLFdBQVU7Ozs7OztzREFDdEIsOERBQUNVOzRDQUFFVixXQUFVO3NEQUFVOzs7Ozs7Ozs7OzsyQ0FHekIzRyxjQUFjeUMsR0FBRyxDQUFDLENBQUMwQiw2QkFDakIsOERBQUN1Qzt3Q0FBMEJDLFdBQVU7a0RBQ25DLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0s7NERBQUdMLFdBQVU7c0VBQWdDeEMsYUFBYTBFLEtBQUs7Ozs7OztzRUFDaEUsOERBQUNuQzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUN2SSx1REFBS0E7b0VBQUMySSxTQUFRO29FQUFVSixXQUFVOzhFQUNoQ3hDLGFBQWFpQixZQUFZOzs7Ozs7OEVBRTVCLDhEQUFDaEgsdURBQUtBO29FQUFDMkksU0FBUTtvRUFBVUosV0FBVTs7d0VBQ2hDeEMsYUFBYUUsU0FBUyxDQUFDQyxNQUFNO3dFQUFDOzs7Ozs7Ozs7Ozs7O3NFQUduQyw4REFBQytDOzREQUFFVixXQUFVO3NFQUF1Q3hDLGFBQWFrQixJQUFJOzs7Ozs7Ozs7Ozs7OERBRXZFLDhEQUFDcUI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDOUkseURBQU1BOzREQUNMa0osU0FBUTs0REFDUlUsTUFBSzs0REFDTFosU0FBUztnRUFDUCxJQUFJMUMsYUFBYUUsU0FBUyxDQUFDQyxNQUFNLEdBQUcsR0FBRztvRUFDckM3RSx3QkFBd0IwRSxhQUFhRSxTQUFTLENBQUMsRUFBRTtvRUFDakR4RSxrQkFBa0I7Z0VBQ3BCOzREQUNGOzREQUNBOEcsV0FBVTtzRUFFViw0RUFBQ2hJLGdIQUFHQTtnRUFBQ2dJLFdBQVU7Ozs7Ozs7Ozs7O3NFQUVqQiw4REFBQzlJLHlEQUFNQTs0REFDTGtKLFNBQVE7NERBQ1JVLE1BQUs7NERBQ0xaLFNBQVMsSUFBTTlCLGFBQWFaLGFBQWEzQyxFQUFFOzREQUMzQ21GLFdBQVU7c0VBRVYsNEVBQUNuSSxnSEFBTUE7Z0VBQUNtSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0FsQ2hCeEMsYUFBYTNDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBK0MzQztHQTkzQndCcEM7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVklDT1xcT25lRHJpdmVcXERvY3VtZW50b3NcXDAuIFR1UmVzaUJvXFxjbGllbnRcXGFwcFxcdXBsb2FkXFxjdWVzdGlvbmFyaW8udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCI7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCI7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiO1xuaW1wb3J0IHsgVXBsb2FkLCBIZWxwQ2lyY2xlLCBUcmFzaDIsIFBsdXMsIFgsIEVkaXQsIEV5ZSB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IEFsZXJ0LCBBbGVydERlc2NyaXB0aW9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9hbGVydFwiO1xuXG5pbnRlcmZhY2UgRXNwZWNpYWxpZGFkVGVtYURhdGEge1xuICBlc3BlY2lhbGlkYWQ6IHN0cmluZ1xuICB0ZW1hOiBzdHJpbmdcbiAgdGl0dWxvOiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIEN1ZXN0aW9uYXJpb0NvbXBvbmVudFByb3BzIHtcbiAgc2hhcmVkRGF0YTogRXNwZWNpYWxpZGFkVGVtYURhdGFcbn1cblxuaW50ZXJmYWNlIE9wY2lvbkRhdGEge1xuICBpZDogc3RyaW5nO1xuICBvcGNpb246IHN0cmluZztcbiAgZXNfY29ycmVjdGE6IGJvb2xlYW47XG4gIGV4cGxpY2FjaW9uOiBzdHJpbmc7XG4gIGltYWdlbl91cmw/OiBzdHJpbmc7XG4gIGltYWdlbl9wYXRoPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgUHJlZ3VudGFEYXRhIHtcbiAgaWQ6IHN0cmluZztcbiAgcHJlZ3VudGE6IHN0cmluZztcbiAgcmVzcHVlc3RhX2NvcnJlY3RhOiBzdHJpbmc7XG4gIGV4cGxpY2FjaW9uX2dlbmVyYWw6IHN0cmluZztcbiAgaW1hZ2VuX3VybD86IHN0cmluZztcbiAgaW1hZ2VuX3BhdGg/OiBzdHJpbmc7XG4gIG9wY2lvbmVzOiBPcGNpb25EYXRhW107XG59XG5cbmludGVyZmFjZSBPcGNpb25SZXNwdWVzdGEge1xuICBvcGNpb246IHN0cmluZztcbiAgZXNfY29ycmVjdGE6IGJvb2xlYW47XG4gIGV4cGxpY2FjaW9uOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBDdWVzdGlvbmFyaW9Kc29uIHtcbiAgaWQ6IHN0cmluZztcbiAgcHJlZ3VudGE6IHN0cmluZztcbiAgb3BjaW9uZXM6IE9wY2lvblJlc3B1ZXN0YVtdO1xuICByZXNwdWVzdGFfY29ycmVjdGE6IHN0cmluZztcbiAgZXhwbGljYWNpb25fZ2VuZXJhbDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgQ3Vlc3Rpb25hcmlvIHtcbiAgaWQ6IG51bWJlcjtcbiAgdGl0bGU6IHN0cmluZztcbiAgZXNwZWNpYWxpZGFkOiBzdHJpbmc7XG4gIHRlbWE6IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICBwcmVndW50YXM6IFByZWd1bnRhRGF0YVtdO1xufVxuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IEFQSSBiYXNlIFVSTFxuZnVuY3Rpb24gZ2V0QXBpQmFzZVVybCgpOiBzdHJpbmcge1xuICBjb25zdCBiYXNlID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMPy5yZXBsYWNlKC9cXC8kLywgJycpO1xuICByZXR1cm4gYmFzZSB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ3Vlc3Rpb25hcmlvQ29tcG9uZW50KHsgc2hhcmVkRGF0YSB9OiBDdWVzdGlvbmFyaW9Db21wb25lbnRQcm9wcykge1xuICAvLyBTdGF0ZSBtYW5hZ2VtZW50XG4gIGNvbnN0IFtjdWVzdGlvbmFyaW9zRGF0YSwgc2V0Q3Vlc3Rpb25hcmlvc0RhdGFdID0gdXNlU3RhdGU8UHJlZ3VudGFEYXRhW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkQ3Vlc3Rpb25hcmlvLCBzZXRTZWxlY3RlZEN1ZXN0aW9uYXJpb10gPSB1c2VTdGF0ZTxQcmVndW50YURhdGEgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkQ3Vlc3Rpb25hcmlvSWQsIHNldFNlbGVjdGVkQ3Vlc3Rpb25hcmlvSWRdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaG93RGV0YWlsVmlldywgc2V0U2hvd0RldGFpbFZpZXddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdGluZ0N1ZXN0aW9uYXJpbywgc2V0RWRpdGluZ0N1ZXN0aW9uYXJpb10gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2N1ZXN0aW9uYXJpb3MsIHNldEN1ZXN0aW9uYXJpb3NdID0gdXNlU3RhdGU8Q3Vlc3Rpb25hcmlvW10+KFtdKTtcbiAgY29uc3QgW2lzVXBsb2FkaW5nLCBzZXRJc1VwbG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdWNjZXNzLCBzZXRTdWNjZXNzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2pzb25Db250ZW50LCBzZXRKc29uQ29udGVudF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFt1cGxvYWRpbmdJbWFnZXMsIHNldFVwbG9hZGluZ0ltYWdlc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKTtcblxuICAvLyBGZXRjaCBjdWVzdGlvbmFyaW9zIHdoZW4gY29tcG9uZW50IG1vdW50c1xuICBjb25zdCBmZXRjaEN1ZXN0aW9uYXJpb3MgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7Z2V0QXBpQmFzZVVybCgpfS9hcGkvdjEvdXBsb2FkLWNvbnRlbnQvY3Vlc3Rpb25hcmlvc2ApO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdFcnJvciBhbCBjYXJnYXIgbG9zIGN1ZXN0aW9uYXJpb3MnKTtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBzZXRDdWVzdGlvbmFyaW9zKGRhdGEpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY3Vlc3Rpb25hcmlvczonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoJ0Vycm9yIGFsIGNhcmdhciBsb3MgY3Vlc3Rpb25hcmlvcycpO1xuICAgIH1cbiAgfTtcblxuICAvLyBMb2FkIGN1ZXN0aW9uYXJpb3Mgb24gbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEN1ZXN0aW9uYXJpb3MoKTtcbiAgfSwgW10pO1xuXG4gIC8vIE5ldyBzdHJ1Y3R1cmUgZnVuY3Rpb25zXG4gIGNvbnN0IGFkZEN1ZXN0aW9uYXJpbyA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdDdWVzdGlvbmFyaW86IFByZWd1bnRhRGF0YSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICBwcmVndW50YTogJycsXG4gICAgICByZXNwdWVzdGFfY29ycmVjdGE6ICdBJyxcbiAgICAgIGV4cGxpY2FjaW9uX2dlbmVyYWw6ICcnLFxuICAgICAgb3BjaW9uZXM6IFtcbiAgICAgICAgeyBpZDogJzEnLCBvcGNpb246ICdBLiAnLCBlc19jb3JyZWN0YTogdHJ1ZSwgZXhwbGljYWNpb246ICcnIH0sXG4gICAgICAgIHsgaWQ6ICcyJywgb3BjaW9uOiAnQi4gJywgZXNfY29ycmVjdGE6IGZhbHNlLCBleHBsaWNhY2lvbjogJycgfSxcbiAgICAgICAgeyBpZDogJzMnLCBvcGNpb246ICdDLiAnLCBlc19jb3JyZWN0YTogZmFsc2UsIGV4cGxpY2FjaW9uOiAnJyB9LFxuICAgICAgICB7IGlkOiAnNCcsIG9wY2lvbjogJ0QuICcsIGVzX2NvcnJlY3RhOiBmYWxzZSwgZXhwbGljYWNpb246ICcnIH1cbiAgICAgIF1cbiAgICB9O1xuICAgIHNldEN1ZXN0aW9uYXJpb3NEYXRhKFsuLi5jdWVzdGlvbmFyaW9zRGF0YSwgbmV3Q3Vlc3Rpb25hcmlvXSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlQ3Vlc3Rpb25hcmlvID0gKGlkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDdWVzdGlvbmFyaW9zRGF0YShjdWVzdGlvbmFyaW9zRGF0YS5maWx0ZXIoYyA9PiBjLmlkICE9PSBpZCkpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZUN1ZXN0aW9uYXJpbyA9IChpZDogc3RyaW5nLCBmaWVsZDoga2V5b2YgUHJlZ3VudGFEYXRhLCB2YWx1ZTogYW55KSA9PiB7XG4gICAgc2V0Q3Vlc3Rpb25hcmlvc0RhdGEoY3Vlc3Rpb25hcmlvc0RhdGEubWFwKGMgPT4gXG4gICAgICBjLmlkID09PSBpZCA/IHsgLi4uYywgW2ZpZWxkXTogdmFsdWUgfSA6IGNcbiAgICApKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVPcGNpb24gPSAoY3Vlc3Rpb25hcmlvSWQ6IHN0cmluZywgb3BjaW9uSWQ6IHN0cmluZywgZmllbGQ6IGtleW9mIE9wY2lvbkRhdGEsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDdWVzdGlvbmFyaW9zRGF0YShjdWVzdGlvbmFyaW9zRGF0YS5tYXAoYyA9PiBcbiAgICAgIGMuaWQgPT09IGN1ZXN0aW9uYXJpb0lkIFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIC4uLmMsXG4gICAgICAgICAgICBvcGNpb25lczogYy5vcGNpb25lcy5tYXAobyA9PiBcbiAgICAgICAgICAgICAgby5pZCA9PT0gb3BjaW9uSWQgPyB7IC4uLm8sIFtmaWVsZF06IHZhbHVlIH0gOiBvXG4gICAgICAgICAgICApXG4gICAgICAgICAgfVxuICAgICAgICA6IGNcbiAgICApKTtcbiAgfTtcblxuICBjb25zdCBzZXRDb3JyZWN0QW5zd2VyID0gKGN1ZXN0aW9uYXJpb0lkOiBzdHJpbmcsIHJlc3B1ZXN0YUNvcnJlY3RhOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDdWVzdGlvbmFyaW9zRGF0YShjdWVzdGlvbmFyaW9zRGF0YS5tYXAoYyA9PiBcbiAgICAgIGMuaWQgPT09IGN1ZXN0aW9uYXJpb0lkIFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIC4uLmMsXG4gICAgICAgICAgICByZXNwdWVzdGFfY29ycmVjdGE6IHJlc3B1ZXN0YUNvcnJlY3RhLFxuICAgICAgICAgICAgb3BjaW9uZXM6IGMub3BjaW9uZXMubWFwKG8gPT4gKHtcbiAgICAgICAgICAgICAgLi4ubyxcbiAgICAgICAgICAgICAgZXNfY29ycmVjdGE6IG8ub3BjaW9uLnN0YXJ0c1dpdGgocmVzcHVlc3RhQ29ycmVjdGEgKyAnLicpXG4gICAgICAgICAgICB9KSlcbiAgICAgICAgICB9XG4gICAgICAgIDogY1xuICAgICkpO1xuICB9O1xuXG4gIC8vIENSVUQgZnVuY3Rpb25zIGZvciBleGlzdGluZyBjdWVzdGlvbmFyaW9zXG4gIGNvbnN0IHVwZGF0ZUN1ZXN0aW9uYXJpb0luREIgPSBhc3luYyAoY3Vlc3Rpb25hcmlvSWQ6IG51bWJlciwgdXBkYXRlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7Z2V0QXBpQmFzZVVybCgpfS9hcGkvdjEvdXBsb2FkLWNvbnRlbnQvY3Vlc3Rpb25hcmlvcy8ke2N1ZXN0aW9uYXJpb0lkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVwZGF0ZXMpLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciBhbCBhY3R1YWxpemFyIGVsIGN1ZXN0aW9uYXJpbycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB1cGRhdGVkQ3Vlc3Rpb25hcmlvID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgXG4gICAgICAvLyBVcGRhdGUgbG9jYWwgc3RhdGVcbiAgICAgIHNldEN1ZXN0aW9uYXJpb3MoY3Vlc3Rpb25hcmlvcy5tYXAoYyA9PiBcbiAgICAgICAgYy5pZCA9PT0gY3Vlc3Rpb25hcmlvSWQgPyB1cGRhdGVkQ3Vlc3Rpb25hcmlvIDogY1xuICAgICAgKSk7XG4gICAgICBcbiAgICAgIHJldHVybiB1cGRhdGVkQ3Vlc3Rpb25hcmlvO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgY3Vlc3Rpb25hcmlvOicsIGVycik7XG4gICAgICBzZXRFcnJvcignRXJyb3IgYWwgYWN0dWFsaXphciBlbCBjdWVzdGlvbmFyaW8nKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZGVsZXRlQ3Vlc3Rpb25hcmlvRnJvbURCID0gYXN5bmMgKGN1ZXN0aW9uYXJpb0lkOiBudW1iZXIpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtnZXRBcGlCYXNlVXJsKCl9L2FwaS92MS91cGxvYWQtY29udGVudC9jdWVzdGlvbmFyaW9zLyR7Y3Vlc3Rpb25hcmlvSWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciBhbCBlbGltaW5hciBlbCBjdWVzdGlvbmFyaW8nKTtcbiAgICAgIH1cblxuICAgICAgLy8gVXBkYXRlIGxvY2FsIHN0YXRlXG4gICAgICBzZXRDdWVzdGlvbmFyaW9zKGN1ZXN0aW9uYXJpb3MuZmlsdGVyKGMgPT4gYy5pZCAhPT0gY3Vlc3Rpb25hcmlvSWQpKTtcbiAgICAgIFxuICAgICAgaWYgKHNlbGVjdGVkQ3Vlc3Rpb25hcmlvICYmIHNlbGVjdGVkQ3Vlc3Rpb25hcmlvLmlkID09PSBjdWVzdGlvbmFyaW9JZC50b1N0cmluZygpKSB7XG4gICAgICAgIHNldFNlbGVjdGVkQ3Vlc3Rpb25hcmlvKG51bGwpO1xuICAgICAgICBzZXRTaG93RGV0YWlsVmlldyhmYWxzZSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBjdWVzdGlvbmFyaW86JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdFcnJvciBhbCBlbGltaW5hciBlbCBjdWVzdGlvbmFyaW8nKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSW1hZ2UgdXBsb2FkIGZ1bmN0aW9uc1xuICBjb25zdCB1cGxvYWRJbWFnZSA9IGFzeW5jIChmaWxlOiBGaWxlLCB0eXBlOiAncHJlZ3VudGEnIHwgJ29wY2lvbicsIGN1ZXN0aW9uYXJpb0lkOiBudW1iZXIsIG9wY2lvbklkPzogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgdXBsb2FkSWQgPSBgJHtjdWVzdGlvbmFyaW9JZH0tJHt0eXBlfS0ke29wY2lvbklkIHx8ICdwcmVndW50YSd9YDtcbiAgICBzZXRVcGxvYWRpbmdJbWFnZXMocHJldiA9PiBuZXcgU2V0KFsuLi5wcmV2LCB1cGxvYWRJZF0pKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGZpbGUpO1xuICAgICAgZm9ybURhdGEuYXBwZW5kKCdjb250ZW50X3R5cGUnLCAnY3Vlc3Rpb25hcmlvcycpO1xuICAgICAgZm9ybURhdGEuYXBwZW5kKCdjb250ZW50X2lkJywgY3Vlc3Rpb25hcmlvSWQudG9TdHJpbmcoKSk7XG4gICAgICBcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7Z2V0QXBpQmFzZVVybCgpfS9hcGkvdjEvdXBsb2FkLWNvbnRlbnQvdXBsb2FkLWltYWdlYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogZm9ybURhdGEsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Vycm9yIGFsIHN1YmlyIGxhIGltYWdlbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSB0aGUgY3Vlc3Rpb25hcmlvIHdpdGggdGhlIG5ldyBpbWFnZSBVUkxcbiAgICAgIGNvbnN0IGN1ZXN0aW9uYXJpbyA9IGN1ZXN0aW9uYXJpb3MuZmluZChjID0+IGMuaWQgPT09IGN1ZXN0aW9uYXJpb0lkKTtcbiAgICAgIGlmIChjdWVzdGlvbmFyaW8gJiYgY3Vlc3Rpb25hcmlvLnByZWd1bnRhcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IHByZWd1bnRhID0gY3Vlc3Rpb25hcmlvLnByZWd1bnRhc1swXTtcbiAgICAgICAgXG4gICAgICAgIGlmICh0eXBlID09PSAncHJlZ3VudGEnKSB7XG4gICAgICAgICAgcHJlZ3VudGEuaW1hZ2VuX3VybCA9IHJlc3VsdC51cmw7XG4gICAgICAgICAgcHJlZ3VudGEuaW1hZ2VuX3BhdGggPSByZXN1bHQucGF0aDtcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAnb3BjaW9uJyAmJiBvcGNpb25JZCkge1xuICAgICAgICAgIGNvbnN0IG9wY2lvbiA9IHByZWd1bnRhLm9wY2lvbmVzLmZpbmQobyA9PiBvLmlkID09PSBvcGNpb25JZCk7XG4gICAgICAgICAgaWYgKG9wY2lvbikge1xuICAgICAgICAgICAgb3BjaW9uLmltYWdlbl91cmwgPSByZXN1bHQudXJsO1xuICAgICAgICAgICAgb3BjaW9uLmltYWdlbl9wYXRoID0gcmVzdWx0LnBhdGg7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyBVcGRhdGUgaW4gZGF0YWJhc2VcbiAgICAgICAgYXdhaXQgdXBkYXRlQ3Vlc3Rpb25hcmlvSW5EQihjdWVzdGlvbmFyaW9JZCwgeyBwcmVndW50YXM6IGN1ZXN0aW9uYXJpby5wcmVndW50YXMgfSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGxvYWRpbmcgaW1hZ2U6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdFcnJvciBhbCBzdWJpciBsYSBpbWFnZW4nKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0VXBsb2FkaW5nSW1hZ2VzKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICBuZXdTZXQuZGVsZXRlKHVwbG9hZElkKTtcbiAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZW1vdmVJbWFnZSA9IGFzeW5jIChjdWVzdGlvbmFyaW9JZDogbnVtYmVyLCB0eXBlOiAncHJlZ3VudGEnIHwgJ29wY2lvbicsIG9wY2lvbklkPzogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGN1ZXN0aW9uYXJpbyA9IGN1ZXN0aW9uYXJpb3MuZmluZChjID0+IGMuaWQgPT09IGN1ZXN0aW9uYXJpb0lkKTtcbiAgICAgIGlmIChjdWVzdGlvbmFyaW8gJiYgY3Vlc3Rpb25hcmlvLnByZWd1bnRhcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IHByZWd1bnRhID0gY3Vlc3Rpb25hcmlvLnByZWd1bnRhc1swXTtcbiAgICAgICAgXG4gICAgICAgIGlmICh0eXBlID09PSAncHJlZ3VudGEnKSB7XG4gICAgICAgICAgcHJlZ3VudGEuaW1hZ2VuX3VybCA9IHVuZGVmaW5lZDtcbiAgICAgICAgICBwcmVndW50YS5pbWFnZW5fcGF0aCA9IHVuZGVmaW5lZDtcbiAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAnb3BjaW9uJyAmJiBvcGNpb25JZCkge1xuICAgICAgICAgIGNvbnN0IG9wY2lvbiA9IHByZWd1bnRhLm9wY2lvbmVzLmZpbmQobyA9PiBvLmlkID09PSBvcGNpb25JZCk7XG4gICAgICAgICAgaWYgKG9wY2lvbikge1xuICAgICAgICAgICAgb3BjaW9uLmltYWdlbl91cmwgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICBvcGNpb24uaW1hZ2VuX3BhdGggPSB1bmRlZmluZWQ7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICAvLyBVcGRhdGUgaW4gZGF0YWJhc2VcbiAgICAgICAgYXdhaXQgdXBkYXRlQ3Vlc3Rpb25hcmlvSW5EQihjdWVzdGlvbmFyaW9JZCwgeyBwcmVndW50YXM6IGN1ZXN0aW9uYXJpby5wcmVndW50YXMgfSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZW1vdmluZyBpbWFnZTonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoJ0Vycm9yIGFsIGVsaW1pbmFyIGxhIGltYWdlbicpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGUgPSBhc3luYyAoY3Vlc3Rpb25hcmlvSWQ6IG51bWJlcikgPT4ge1xuICAgIGlmICghY29uZmlybSgnwr9Fc3TDoXMgc2VndXJvIGRlIHF1ZSBxdWllcmVzIGVsaW1pbmFyIGVzdGUgY3Vlc3Rpb25hcmlvPycpKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtnZXRBcGlCYXNlVXJsKCl9L2FwaS92MS91cGxvYWQtY29udGVudC9jdWVzdGlvbmFyaW9zLyR7Y3Vlc3Rpb25hcmlvSWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRXJyb3IgYWwgZWxpbWluYXIgZWwgY3Vlc3Rpb25hcmlvJyk7XG5cbiAgICAgIHNldEN1ZXN0aW9uYXJpb3MoY3Vlc3Rpb25hcmlvcy5maWx0ZXIoYyA9PiBjLmlkICE9PSBjdWVzdGlvbmFyaW9JZCkpO1xuICAgICAgc2V0U3VjY2Vzcyh0cnVlKTtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0U3VjY2VzcyhmYWxzZSksIDMwMDApO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgY3Vlc3Rpb25hcmlvOicsIGVycik7XG4gICAgICBzZXRFcnJvcignRXJyb3IgYWwgZWxpbWluYXIgZWwgY3Vlc3Rpb25hcmlvJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEhhbmRsZSBKU09OIGNvbnRlbnQgc3VibWlzc2lvblxuICBjb25zdCBoYW5kbGVKc29uU3VibWl0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghanNvbkNvbnRlbnQudHJpbSgpKSB7XG4gICAgICBzZXRFcnJvcignUG9yIGZhdm9yIGluZ3Jlc2UgZWwgY29udGVuaWRvIEpTT04nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoIXNoYXJlZERhdGEuZXNwZWNpYWxpZGFkIHx8ICFzaGFyZWREYXRhLnRlbWEpIHtcbiAgICAgIHNldEVycm9yKCdQb3IgZmF2b3IgY29tcGxldGUgbGEgaW5mb3JtYWNpw7NuIGdlbmVyYWwgKGVzcGVjaWFsaWRhZCB5IHRlbWEpIGFudGVzIGRlIHByb2Nlc2FyIGVsIEpTT04nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc1VwbG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBzZXRTdWNjZXNzKGZhbHNlKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBWYWxpZGF0ZSBKU09OIGZvcm1hdCBmaXJzdFxuICAgICAgbGV0IHBhcnNlZEpzb247XG4gICAgICB0cnkge1xuICAgICAgICBwYXJzZWRKc29uID0gSlNPTi5wYXJzZShqc29uQ29udGVudCk7XG4gICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRm9ybWF0byBKU09OIGludsOhbGlkbycpO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSBzdHJ1Y3R1cmVcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheShwYXJzZWRKc29uKSB8fCBwYXJzZWRKc29uLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0VsIEpTT04gZGViZSBjb250ZW5lciB1bmEgbGlzdGEgZGUgY3Vlc3Rpb25hcmlvcycpO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICAgIGNvbnN0IHJlcXVpcmVkRmllbGRzID0gWydpZCcsICdwcmVndW50YScsICdvcGNpb25lcycsICdyZXNwdWVzdGFfY29ycmVjdGEnLCAnZXhwbGljYWNpb25fZ2VuZXJhbCddO1xuICAgICAgZm9yIChjb25zdCBjdWVzdGlvbmFyaW8gb2YgcGFyc2VkSnNvbikge1xuICAgICAgICBmb3IgKGNvbnN0IGZpZWxkIG9mIHJlcXVpcmVkRmllbGRzKSB7XG4gICAgICAgICAgaWYgKCEoZmllbGQgaW4gY3Vlc3Rpb25hcmlvKSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW1wbyByZXF1ZXJpZG8gZmFsdGFudGU6ICR7ZmllbGR9YCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ2pzb25fY29udGVudCcsIGpzb25Db250ZW50KTtcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnZXNwZWNpYWxpZGFkJywgc2hhcmVkRGF0YS5lc3BlY2lhbGlkYWQpO1xuICAgICAgZm9ybURhdGEuYXBwZW5kKCdzaXN0ZW1hJywgJ1R1UmVzaUJvJyk7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ3RlbWEnLCBzaGFyZWREYXRhLnRlbWEpO1xuICAgICAgaWYgKHNoYXJlZERhdGEudGl0dWxvKSB7XG4gICAgICAgIGZvcm1EYXRhLmFwcGVuZCgndGl0dWxvJywgc2hhcmVkRGF0YS50aXR1bG8pO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2dldEFwaUJhc2VVcmwoKX0vYXBpL3YxL3VwbG9hZC1jb250ZW50L2N1ZXN0aW9uYXJpb3MvanNvbmAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGJvZHk6IGZvcm1EYXRhLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmRldGFpbCB8fCAnRXJyb3IgYWwgcHJvY2VzYXIgZWwgY29udGVuaWRvIEpTT04nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0U3VjY2Vzcyh0cnVlKTtcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICAgIHNldEpzb25Db250ZW50KCcnKTtcbiAgICAgICAgZmV0Y2hDdWVzdGlvbmFyaW9zKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0Lm1lc3NhZ2UgfHwgJ0Vycm9yIGFsIHByb2Nlc2FyIGVsIGNvbnRlbmlkbyBKU09OJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwcm9jZXNzaW5nIEpTT046JywgZXJyKTtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRXJyb3IgYWwgcHJvY2VzYXIgZWwgY29udGVuaWRvIEpTT04nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNVcGxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBFbmhhbmNlZCBkZXRhaWxlZCB2aWV3IGNvbXBvbmVudCB3aXRoIGlubGluZSBlZGl0aW5nXG4gIGNvbnN0IERldGFpbGVkVmlldyA9ICh7IGN1ZXN0aW9uYXJpbywgY3Vlc3Rpb25hcmlvSWQsIGlzRWRpdGluZywgb25Ub2dnbGVFZGl0IH06IHtcbiAgICBjdWVzdGlvbmFyaW86IFByZWd1bnRhRGF0YTtcbiAgICBjdWVzdGlvbmFyaW9JZDogbnVtYmVyO1xuICAgIGlzRWRpdGluZzogYm9vbGVhbjtcbiAgICBvblRvZ2dsZUVkaXQ6ICgpID0+IHZvaWQ7XG4gIH0pID0+IHtcbiAgICBjb25zdCBbbG9jYWxDdWVzdGlvbmFyaW8sIHNldExvY2FsQ3Vlc3Rpb25hcmlvXSA9IHVzZVN0YXRlPFByZWd1bnRhRGF0YT4oY3Vlc3Rpb25hcmlvKTtcbiAgICBjb25zdCBbaXNTYXZpbmcsIHNldElzU2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAgIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgICBzZXRJc1NhdmluZyh0cnVlKTtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHVwZGF0ZUN1ZXN0aW9uYXJpb0luREIoY3Vlc3Rpb25hcmlvSWQsIHsgcHJlZ3VudGFzOiBbbG9jYWxDdWVzdGlvbmFyaW9dIH0pO1xuICAgICAgICBvblRvZ2dsZUVkaXQoKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmc6JywgZXJyKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldElzU2F2aW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlSW1hZ2VVcGxvYWQgPSBhc3luYyAoZmlsZTogRmlsZSwgdHlwZTogJ3ByZWd1bnRhJyB8ICdvcGNpb24nLCBvcGNpb25JZD86IHN0cmluZykgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgdXBsb2FkSW1hZ2UoZmlsZSwgdHlwZSwgY3Vlc3Rpb25hcmlvSWQsIG9wY2lvbklkKTtcbiAgICAgICAgLy8gUmVmcmVzaCB0aGUgY3Vlc3Rpb25hcmlvIGRhdGFcbiAgICAgICAgZmV0Y2hDdWVzdGlvbmFyaW9zKCk7XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBsb2FkaW5nIGltYWdlOicsIGVycik7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGNvbnN0IGhhbmRsZUltYWdlUmVtb3ZlID0gYXN5bmMgKHR5cGU6ICdwcmVndW50YScgfCAnb3BjaW9uJywgb3BjaW9uSWQ/OiBzdHJpbmcpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHJlbW92ZUltYWdlKGN1ZXN0aW9uYXJpb0lkLCB0eXBlLCBvcGNpb25JZCk7XG4gICAgICAgIC8vIFJlZnJlc2ggdGhlIGN1ZXN0aW9uYXJpbyBkYXRhXG4gICAgICAgIGZldGNoQ3Vlc3Rpb25hcmlvcygpO1xuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlbW92aW5nIGltYWdlOicsIGVycik7XG4gICAgICB9XG4gICAgfTtcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogSGVhZGVyIHdpdGggRWRpdCBDb250cm9scyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkN1ZXN0aW9uYXJpbyBEZXRhbGxhZG88L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAge2lzRWRpdGluZyA/IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU2F2aW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzU2F2aW5nID8gJ0d1YXJkYW5kby4uLicgOiAnR3VhcmRhcid9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25Ub2dnbGVFZGl0fVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENhbmNlbGFyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlRWRpdH1cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBFZGl0YXJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUXVlc3Rpb24gU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0yMDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtYmx1ZS05MDAgbWItM1wiPlByZWd1bnRhPC9oMz5cbiAgICAgICAgICB7aXNFZGl0aW5nID8gKFxuICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgIHZhbHVlPXtsb2NhbEN1ZXN0aW9uYXJpby5wcmVndW50YX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRMb2NhbEN1ZXN0aW9uYXJpbyh7Li4ubG9jYWxDdWVzdGlvbmFyaW8sIHByZWd1bnRhOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi00XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS04MDAgbGVhZGluZy1yZWxheGVkIG1iLTRcIj57Y3Vlc3Rpb25hcmlvLnByZWd1bnRhfTwvcD5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIEltYWdlIFVwbG9hZC9EaXNwbGF5IGZvciBRdWVzdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAge2N1ZXN0aW9uYXJpby5pbWFnZW5fdXJsID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPXtjdWVzdGlvbmFyaW8uaW1hZ2VuX3VybH1cbiAgICAgICAgICAgICAgICAgIGFsdD1cIkltYWdlbiBkZSBsYSBwcmVndW50YVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1mdWxsIGgtYXV0byByb3VuZGVkLWxnIGJvcmRlclwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7aXNFZGl0aW5nICYmIChcbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlSW1hZ2VSZW1vdmUoJ3ByZWd1bnRhJyl9XG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIHJpZ2h0LTJcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IGlzRWRpdGluZyAmJiAoXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5BZ3JlZ2FyIGltYWdlbiBhIGxhIHByZWd1bnRhPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlLypcIlxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSBlLnRhcmdldC5maWxlcz8uWzBdO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZmlsZSkgaGFuZGxlSW1hZ2VVcGxvYWQoZmlsZSwgJ3ByZWd1bnRhJyk7XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMVwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgey8qIE9wdGlvbnMgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5PcGNpb25lcyBkZSBSZXNwdWVzdGE8L2gzPlxuICAgICAgICAgIHsoaXNFZGl0aW5nID8gbG9jYWxDdWVzdGlvbmFyaW8ub3BjaW9uZXMgOiBjdWVzdGlvbmFyaW8ub3BjaW9uZXMpLm1hcCgob3BjaW9uLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e29wY2lvbi5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYm9yZGVyIHJvdW5kZWQtbGcgcC00ICR7XG4gICAgICAgICAgICAgICAgb3BjaW9uLmVzX2NvcnJlY3RhXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItZ3JlZW4tMzAwIGJnLWdyZWVuLTUwJ1xuICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwIGJnLXdoaXRlJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4LXNocmluay0wIHctNiBoLTYgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgIG9wY2lvbi5lc19jb3JyZWN0YVxuICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7U3RyaW5nLmZyb21DaGFyQ29kZSg2NSArIGluZGV4KX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBPcHRpb24gVGV4dCAqL31cbiAgICAgICAgICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtvcGNpb24ub3BjaW9ufVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZE9wY2lvbmVzID0gbG9jYWxDdWVzdGlvbmFyaW8ub3BjaW9uZXMubWFwKG8gPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgby5pZCA9PT0gb3BjaW9uLmlkID8gey4uLm8sIG9wY2lvbjogZS50YXJnZXQudmFsdWV9IDogb1xuICAgICAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldExvY2FsQ3Vlc3Rpb25hcmlvKHsuLi5sb2NhbEN1ZXN0aW9uYXJpbywgb3BjaW9uZXM6IHVwZGF0ZWRPcGNpb25lc30pO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntvcGNpb24ub3BjaW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBPcHRpb24gRXhwbGFuYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkIHAtM1wiPlxuICAgICAgICAgICAgICAgICAgICB7aXNFZGl0aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e29wY2lvbi5leHBsaWNhY2lvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkT3BjaW9uZXMgPSBsb2NhbEN1ZXN0aW9uYXJpby5vcGNpb25lcy5tYXAobyA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG8uaWQgPT09IG9wY2lvbi5pZCA/IHsuLi5vLCBleHBsaWNhY2lvbjogZS50YXJnZXQudmFsdWV9IDogb1xuICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRMb2NhbEN1ZXN0aW9uYXJpbyh7Li4ubG9jYWxDdWVzdGlvbmFyaW8sIG9wY2lvbmVzOiB1cGRhdGVkT3BjaW9uZXN9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICByb3dzPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkV4cGxpY2FjacOzbjo8L3N0cm9uZz4ge29wY2lvbi5leHBsaWNhY2lvbn1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIE9wdGlvbiBJbWFnZSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtvcGNpb24uaW1hZ2VuX3VybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17b3BjaW9uLmltYWdlbl91cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17YEltYWdlbiBvcGNpw7NuICR7U3RyaW5nLmZyb21DaGFyQ29kZSg2NSArIGluZGV4KX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy1mdWxsIGgtYXV0byByb3VuZGVkIGJvcmRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAge2lzRWRpdGluZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVJbWFnZVJlbW92ZSgnb3BjaW9uJywgb3BjaW9uLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApIDogaXNFZGl0aW5nICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5BZ3JlZ2FyIGltYWdlbiBhIGxhIG9wY2nDs248L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSBlLnRhcmdldC5maWxlcz8uWzBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWxlKSBoYW5kbGVJbWFnZVVwbG9hZChmaWxlLCAnb3BjaW9uJywgb3BjaW9uLmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBDb3JyZWN0IEFuc3dlciBSYWRpbyAqL31cbiAgICAgICAgICAgICAgICAgIHtpc0VkaXRpbmcgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicmFkaW9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT17YGNvcnJlY3QtYW5zd2VyLSR7Y3Vlc3Rpb25hcmlvSWR9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e29wY2lvbi5lc19jb3JyZWN0YX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGxldHRlciA9IFN0cmluZy5mcm9tQ2hhckNvZGUoNjUgKyBpbmRleCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRPcGNpb25lcyA9IGxvY2FsQ3Vlc3Rpb25hcmlvLm9wY2lvbmVzLm1hcChvID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ubyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlc19jb3JyZWN0YTogby5pZCA9PT0gb3BjaW9uLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0TG9jYWxDdWVzdGlvbmFyaW8oe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmxvY2FsQ3Vlc3Rpb25hcmlvLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wY2lvbmVzOiB1cGRhdGVkT3BjaW9uZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzcHVlc3RhX2NvcnJlY3RhOiBsZXR0ZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlJlc3B1ZXN0YSBjb3JyZWN0YTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIENvcnJlY3QgQW5zd2VyIEluZGljYXRvciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tOTAwIG1iLTJcIj5SZXNwdWVzdGEgQ29ycmVjdGE8L2g0PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwXCI+T3BjacOzbiB7Y3Vlc3Rpb25hcmlvLnJlc3B1ZXN0YV9jb3JyZWN0YX08L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBHZW5lcmFsIEV4cGxhbmF0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy01MCBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQteWVsbG93LTkwMCBtYi0zXCI+RXhwbGljYWNpw7NuIEdlbmVyYWw8L2gzPlxuICAgICAgICAgIHtpc0VkaXRpbmcgPyAoXG4gICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgdmFsdWU9e2xvY2FsQ3Vlc3Rpb25hcmlvLmV4cGxpY2FjaW9uX2dlbmVyYWx9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TG9jYWxDdWVzdGlvbmFyaW8oey4uLmxvY2FsQ3Vlc3Rpb25hcmlvLCBleHBsaWNhY2lvbl9nZW5lcmFsOiBlLnRhcmdldC52YWx1ZX0pfVxuICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMCBsZWFkaW5nLXJlbGF4ZWRcIj57Y3Vlc3Rpb25hcmlvLmV4cGxpY2FjaW9uX2dlbmVyYWx9PC9wPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICAvLyBNYWluIGNvbXBvbmVudCByZW5kZXJcbiAgaWYgKHNob3dEZXRhaWxWaWV3ICYmIHNlbGVjdGVkQ3Vlc3Rpb25hcmlvKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gcC02XCI+XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93RGV0YWlsVmlldyhmYWxzZSl9XG4gICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLTRcIlxuICAgICAgICA+XG4gICAgICAgICAg4oaQIFZvbHZlciBhIGxhIGxpc3RhXG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8RGV0YWlsZWRWaWV3XG4gICAgICAgICAgY3Vlc3Rpb25hcmlvPXtzZWxlY3RlZEN1ZXN0aW9uYXJpb31cbiAgICAgICAgICBjdWVzdGlvbmFyaW9JZD17cGFyc2VJbnQoc2VsZWN0ZWRDdWVzdGlvbmFyaW8uaWQpfVxuICAgICAgICAgIGlzRWRpdGluZz17ZWRpdGluZ0N1ZXN0aW9uYXJpbyA9PT0gcGFyc2VJbnQoc2VsZWN0ZWRDdWVzdGlvbmFyaW8uaWQpfVxuICAgICAgICAgIG9uVG9nZ2xlRWRpdD17KCkgPT4ge1xuICAgICAgICAgICAgaWYgKGVkaXRpbmdDdWVzdGlvbmFyaW8gPT09IHBhcnNlSW50KHNlbGVjdGVkQ3Vlc3Rpb25hcmlvLmlkKSkge1xuICAgICAgICAgICAgICBzZXRFZGl0aW5nQ3Vlc3Rpb25hcmlvKG51bGwpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgc2V0RWRpdGluZ0N1ZXN0aW9uYXJpbyhwYXJzZUludChzZWxlY3RlZEN1ZXN0aW9uYXJpby5pZCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH19XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgIHsvKiBGb3JtICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCI+XG4gICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJvcmRlci0wIHNoYWRvdy1zbVwiPlxuICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTRcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgTnVldm8gQ3Vlc3Rpb25hcmlvXG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgey8qIEpTT04gVXBsb2FkIFNlY3Rpb24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgcC00IGJvcmRlciBib3JkZXItYmx1ZS0yMDAgYmctYmx1ZS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItM1wiPlxuICAgICAgICAgICAgICAgIDxVcGxvYWQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMFwiPkNyZWFyIGRlc2RlIEpTT048L2gzPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICBQZWdhIGVsIGNvbnRlbmlkbyBKU09OIHBhcmEgY3JlYXIgbcO6bHRpcGxlcyBjdWVzdGlvbmFyaW9zIGRlIHVuYSB2ZXouXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIGlkPVwianNvbi1jb250ZW50XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUGVnYSBhcXXDrSBlbCBjb250ZW5pZG8gSlNPTi4uLlwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17anNvbkNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEpzb25Db250ZW50KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHJvd3M9ezh9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUpzb25TdWJtaXR9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWpzb25Db250ZW50LnRyaW0oKSB8fCBpc1VwbG9hZGluZ31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VXBsb2FkIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAge2lzVXBsb2FkaW5nID8gJ1Byb2Nlc2FuZG8uLi4nIDogJ0NyZWFyIGRlc2RlIEpTT04nfVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogSlNPTiBGb3JtYXQgRXhhbXBsZSAqL31cbiAgICAgICAgICAgICAgPGRldGFpbHMgY2xhc3NOYW1lPVwibXQtM1wiPlxuICAgICAgICAgICAgICAgIDxzdW1tYXJ5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMCBjdXJzb3ItcG9pbnRlciBob3Zlcjp0ZXh0LWJsdWUtODAwXCI+XG4gICAgICAgICAgICAgICAgICBWZXIgZm9ybWF0byBKU09OIHJlcXVlcmlkb1xuICAgICAgICAgICAgICAgIDwvc3VtbWFyeT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgcC0zIGJnLWdyYXktNTAgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbW9ubyBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIDxwcmU+e2BbXG4gIHtcbiAgICBcImlkXCI6IFwiMVwiLFxuICAgIFwicHJlZ3VudGFcIjogXCLCv0N1w6FsIGVzIGVsIHRyYXRhbWllbnRvIGRlIHByaW1lcmEgbMOtbmVhIHBhcmEgbGEgaGlwZXJ0ZW5zacOzbiBhcnRlcmlhbD9cIixcbiAgICBcIm9wY2lvbmVzXCI6IFtcbiAgICAgIHtcbiAgICAgICAgXCJvcGNpb25cIjogXCJBLiBJbmhpYmlkb3JlcyBkZSBsYSBFQ0EgKElFQ0EpXCIsXG4gICAgICAgIFwiZXNfY29ycmVjdGFcIjogdHJ1ZSxcbiAgICAgICAgXCJleHBsaWNhY2lvblwiOiBcIkxvcyBJRUNBIHNvbiBjb25zaWRlcmFkb3MgdHJhdGFtaWVudG8gZGUgcHJpbWVyYSBsw61uZWEuLi5cIlxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgXCJvcGNpb25cIjogXCJCLiBCZXRhYmxvcXVlYWRvcmVzXCIsXG4gICAgICAgIFwiZXNfY29ycmVjdGFcIjogZmFsc2UsXG4gICAgICAgIFwiZXhwbGljYWNpb25cIjogXCJMb3MgYmV0YWJsb3F1ZWFkb3JlcyBubyBzb24gbGEgcHJpbWVyYSBvcGNpw7NuLi4uXCJcbiAgICAgIH1cbiAgICBdLFxuICAgIFwicmVzcHVlc3RhX2NvcnJlY3RhXCI6IFwiQVwiLFxuICAgIFwiZXhwbGljYWNpb25fZ2VuZXJhbFwiOiBcIkVsIG1hbmVqbyBpbmljaWFsIGRlIGxhIGhpcGVydGVuc2nDs24gc2lndWUgbGFzIGd1w61hcy4uLlwiXG4gIH1cbl1gfTwvcHJlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2RldGFpbHM+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYi02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXItdFwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXgganVzdGlmeS1jZW50ZXIgdGV4dC14cyB1cHBlcmNhc2VcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy13aGl0ZSBweC0yIHRleHQtZ3JheS01MDBcIj5PIGNyZWFyIG1hbnVhbG1lbnRlPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTWFudWFsIENyZWF0aW9uIEZvcm0gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTggcC00IGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIGJnLWdyZWVuLTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi00XCI+XG4gICAgICAgICAgICAgICAgPEhlbHBDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tOTAwXCI+Q3JlYXIgQ3Vlc3Rpb25hcmlvPC9oMz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5DdWVzdGlvbmFyaW9zPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YWRkQ3Vlc3Rpb25hcmlvfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICBBZ3JlZ2FyIEN1ZXN0aW9uYXJpb1xuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Y3Vlc3Rpb25hcmlvc0RhdGEubWFwKChjdWVzdGlvbmFyaW8pID0+IChcbiAgICAgICAgICAgICAgICAgIDxDYXJkIGtleT17Y3Vlc3Rpb25hcmlvLmlkfSBjbGFzc05hbWU9XCJwLTQgYmctd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5DdWVzdGlvbmFyaW8ge2N1ZXN0aW9uYXJpby5pZH08L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlQ3Vlc3Rpb25hcmlvKGN1ZXN0aW9uYXJpby5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFF1ZXN0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBtYi0yIGJsb2NrXCI+UHJlZ3VudGE8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXNjcmliYSBsYSBwcmVndW50YS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjdWVzdGlvbmFyaW8ucHJlZ3VudGF9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQ3Vlc3Rpb25hcmlvKGN1ZXN0aW9uYXJpby5pZCwgJ3ByZWd1bnRhJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBPcHRpb25zICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBtYi0yIGJsb2NrXCI+T3BjaW9uZXM8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2N1ZXN0aW9uYXJpby5vcGNpb25lcy5tYXAoKG9wY2lvbiwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17b3BjaW9uLmlkfSBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZCBwLTMgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7U3RyaW5nLmZyb21DaGFyQ29kZSg2NSArIGluZGV4KX0uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2BPcGNpw7NuICR7U3RyaW5nLmZyb21DaGFyQ29kZSg2NSArIGluZGV4KX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtvcGNpb24ub3BjaW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlT3BjaW9uKGN1ZXN0aW9uYXJpby5pZCwgb3BjaW9uLmlkLCAnb3BjaW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT17YGNvcnJlY3QtJHtjdWVzdGlvbmFyaW8uaWR9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtvcGNpb24uZXNfY29ycmVjdGF9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eygpID0+IHNldENvcnJlY3RBbnN3ZXIoY3Vlc3Rpb25hcmlvLmlkLCBTdHJpbmcuZnJvbUNoYXJDb2RlKDY1ICsgaW5kZXgpKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQgaC00XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXhwbGljYWNpw7NuIGRlIGVzdGEgb3BjacOzbi4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtvcGNpb24uZXhwbGljYWNpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlT3BjaW9uKGN1ZXN0aW9uYXJpby5pZCwgb3BjaW9uLmlkLCAnZXhwbGljYWNpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBHZW5lcmFsIEV4cGxhbmF0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSBtYi0yIGJsb2NrXCI+RXhwbGljYWNpw7NuIEdlbmVyYWw8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXhwbGljYWNpw7NuIGdlbmVyYWwgZGVsIGN1ZXN0aW9uYXJpby4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjdWVzdGlvbmFyaW8uZXhwbGljYWNpb25fZ2VuZXJhbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDdWVzdGlvbmFyaW8oY3Vlc3Rpb25hcmlvLmlkLCAnZXhwbGljYWNpb25fZ2VuZXJhbCcsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICAgICkpfVxuXG4gICAgICAgICAgICAgICAge2N1ZXN0aW9uYXJpb3NEYXRhLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIC8vIENvbnZlcnQgdG8gSlNPTiBhbmQgc3VibWl0XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QganNvbkRhdGEgPSBKU09OLnN0cmluZ2lmeShjdWVzdGlvbmFyaW9zRGF0YSwgbnVsbCwgMik7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0SnNvbkNvbnRlbnQoanNvbkRhdGEpO1xuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUpzb25TdWJtaXQoKTtcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzVXBsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtpc1VwbG9hZGluZyA/ICdDcmVhbmRvLi4uJyA6ICdDcmVhciBDdWVzdGlvbmFyaW9zJ31cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBBbGVydHMgKi99XG4gICAgICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8QWxlcnQgdmFyaWFudD1cImRlc3RydWN0aXZlXCIgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgIDxBbGVydERlc2NyaXB0aW9uPntlcnJvcn08L0FsZXJ0RGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIDwvQWxlcnQ+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7c3VjY2VzcyAmJiAoXG4gICAgICAgICAgICAgIDxBbGVydCBjbGFzc05hbWU9XCJib3JkZXItZ3JlZW4tMjAwIGJnLWdyZWVuLTUwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgwqFDdWVzdGlvbmFyaW9zIGNyZWFkb3MgZXhpdG9zYW1lbnRlIVxuICAgICAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDdWVzdGlvbmFyaW9zIExpc3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTFcIj5cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYm9yZGVyLTAgc2hhZG93LXNtXCI+XG4gICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItNFwiPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+Q3Vlc3Rpb25hcmlvcyAoe2N1ZXN0aW9uYXJpb3MubGVuZ3RofSk8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAge2N1ZXN0aW9uYXJpb3MubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICA8SGVscENpcmNsZSBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi0zIG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbVwiPk5vIGhheSBjdWVzdGlvbmFyaW9zIGHDum48L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgY3Vlc3Rpb25hcmlvcy5tYXAoKGN1ZXN0aW9uYXJpbykgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2N1ZXN0aW9uYXJpby5pZH0gY2xhc3NOYW1lPVwiZ3JvdXAgYm9yZGVyIHJvdW5kZWQtbGcgcC0zIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHRydW5jYXRlXCI+e2N1ZXN0aW9uYXJpby50aXRsZX08L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Vlc3Rpb25hcmlvLmVzcGVjaWFsaWRhZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjdWVzdGlvbmFyaW8ucHJlZ3VudGFzLmxlbmd0aH0gcHJlZ3VudGFzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xIHRydW5jYXRlXCI+e2N1ZXN0aW9uYXJpby50ZW1hfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN1ZXN0aW9uYXJpby5wcmVndW50YXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRDdWVzdGlvbmFyaW8oY3Vlc3Rpb25hcmlvLnByZWd1bnRhc1swXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRTaG93RGV0YWlsVmlldyh0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTcwMCBob3ZlcjpiZy1ibHVlLTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGUoY3Vlc3Rpb25hcmlvLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTAgdGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTcwMCBob3ZlcjpiZy1yZWQtNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCYWRnZSIsIlRleHRhcmVhIiwiVXBsb2FkIiwiSGVscENpcmNsZSIsIlRyYXNoMiIsIlBsdXMiLCJYIiwiRXllIiwiQWxlcnQiLCJBbGVydERlc2NyaXB0aW9uIiwiZ2V0QXBpQmFzZVVybCIsInByb2Nlc3MiLCJiYXNlIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMIiwicmVwbGFjZSIsIkN1ZXN0aW9uYXJpb0NvbXBvbmVudCIsInNoYXJlZERhdGEiLCJjdWVzdGlvbmFyaW9zRGF0YSIsInNldEN1ZXN0aW9uYXJpb3NEYXRhIiwic2VsZWN0ZWRDdWVzdGlvbmFyaW8iLCJzZXRTZWxlY3RlZEN1ZXN0aW9uYXJpbyIsInNlbGVjdGVkQ3Vlc3Rpb25hcmlvSWQiLCJzZXRTZWxlY3RlZEN1ZXN0aW9uYXJpb0lkIiwic2hvd0RldGFpbFZpZXciLCJzZXRTaG93RGV0YWlsVmlldyIsImVkaXRpbmdDdWVzdGlvbmFyaW8iLCJzZXRFZGl0aW5nQ3Vlc3Rpb25hcmlvIiwiY3Vlc3Rpb25hcmlvcyIsInNldEN1ZXN0aW9uYXJpb3MiLCJpc1VwbG9hZGluZyIsInNldElzVXBsb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInN1Y2Nlc3MiLCJzZXRTdWNjZXNzIiwianNvbkNvbnRlbnQiLCJzZXRKc29uQ29udGVudCIsInVwbG9hZGluZ0ltYWdlcyIsInNldFVwbG9hZGluZ0ltYWdlcyIsIlNldCIsImZldGNoQ3Vlc3Rpb25hcmlvcyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJlcnIiLCJjb25zb2xlIiwiYWRkQ3Vlc3Rpb25hcmlvIiwibmV3Q3Vlc3Rpb25hcmlvIiwiaWQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJwcmVndW50YSIsInJlc3B1ZXN0YV9jb3JyZWN0YSIsImV4cGxpY2FjaW9uX2dlbmVyYWwiLCJvcGNpb25lcyIsIm9wY2lvbiIsImVzX2NvcnJlY3RhIiwiZXhwbGljYWNpb24iLCJyZW1vdmVDdWVzdGlvbmFyaW8iLCJmaWx0ZXIiLCJjIiwidXBkYXRlQ3Vlc3Rpb25hcmlvIiwiZmllbGQiLCJ2YWx1ZSIsIm1hcCIsInVwZGF0ZU9wY2lvbiIsImN1ZXN0aW9uYXJpb0lkIiwib3BjaW9uSWQiLCJvIiwic2V0Q29ycmVjdEFuc3dlciIsInJlc3B1ZXN0YUNvcnJlY3RhIiwic3RhcnRzV2l0aCIsInVwZGF0ZUN1ZXN0aW9uYXJpb0luREIiLCJ1cGRhdGVzIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwidXBkYXRlZEN1ZXN0aW9uYXJpbyIsImRlbGV0ZUN1ZXN0aW9uYXJpb0Zyb21EQiIsInVwbG9hZEltYWdlIiwiZmlsZSIsInR5cGUiLCJ1cGxvYWRJZCIsInByZXYiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwicmVzdWx0IiwiY3Vlc3Rpb25hcmlvIiwiZmluZCIsInByZWd1bnRhcyIsImxlbmd0aCIsImltYWdlbl91cmwiLCJ1cmwiLCJpbWFnZW5fcGF0aCIsInBhdGgiLCJuZXdTZXQiLCJkZWxldGUiLCJyZW1vdmVJbWFnZSIsInVuZGVmaW5lZCIsImhhbmRsZURlbGV0ZSIsImNvbmZpcm0iLCJzZXRUaW1lb3V0IiwiaGFuZGxlSnNvblN1Ym1pdCIsInRyaW0iLCJlc3BlY2lhbGlkYWQiLCJ0ZW1hIiwicGFyc2VkSnNvbiIsInBhcnNlIiwicGFyc2VFcnJvciIsIkFycmF5IiwiaXNBcnJheSIsInJlcXVpcmVkRmllbGRzIiwidGl0dWxvIiwiZXJyb3JEYXRhIiwiZGV0YWlsIiwibWVzc2FnZSIsIkRldGFpbGVkVmlldyIsImlzRWRpdGluZyIsIm9uVG9nZ2xlRWRpdCIsImxvY2FsQ3Vlc3Rpb25hcmlvIiwic2V0TG9jYWxDdWVzdGlvbmFyaW8iLCJpc1NhdmluZyIsInNldElzU2F2aW5nIiwiaGFuZGxlU2F2ZSIsImhhbmRsZUltYWdlVXBsb2FkIiwiaGFuZGxlSW1hZ2VSZW1vdmUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInZhcmlhbnQiLCJoMyIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInJvd3MiLCJwIiwiaW1nIiwic3JjIiwiYWx0Iiwic2l6ZSIsImFjY2VwdCIsImZpbGVzIiwiaW5kZXgiLCJTdHJpbmciLCJmcm9tQ2hhckNvZGUiLCJ1cGRhdGVkT3BjaW9uZXMiLCJzdHJvbmciLCJpbnB1dCIsIm5hbWUiLCJjaGVja2VkIiwibGV0dGVyIiwiaDQiLCJwYXJzZUludCIsInBsYWNlaG9sZGVyIiwiZGV0YWlscyIsInN1bW1hcnkiLCJwcmUiLCJzcGFuIiwianNvbkRhdGEiLCJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/upload/cuestionario.tsx\n"));

/***/ })

});